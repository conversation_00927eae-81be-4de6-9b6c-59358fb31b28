<html><head><title></title></head><body><div class='container'><h1 style="color: #333;font-size: 30px;font-weight: 600;font-family: Helvetica, Arial, sans-serif;font-weight:normal;"></h1><div id='editor' style="font-family:Lucida Grande,Helvetica,Arial,sans-serif; font-size: 16px; line-height: 1.42857143;
    color: #333;"><table class="w-full table-fixed">
<thead class="text-left text-xs">
<tr>
<th class="w-2/12 font-medium">Model Name &amp; ID</th>
<th class="w-2/12 font-medium text-center">Input ($/1M tokens)</th>
<th class="w-2/12 font-medium text-center">Output ($/1M tokens)</th>
<th class="w-2/12 font-medium text-center">Context (tokens)</th>
</tr>
</thead>
<tbody class="divide-y">
<tr class="text-sm" title="Horizon Beta">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openrouter/horizon-beta">Horizon Beta</a><code class="text-xs">openrouter/horizon-beta</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">256 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Codestral 2508">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/codestral-2508">Mistral: Codestral 2508</a><code class="text-xs">mistralai/codestral-2508</code></td>
<td class="text-center">
<div>$0,30</div>
</td>
<td class="text-center">
<div>$0,90</div>
</td>
<td><span class="flex justify-center gap-1">256 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 30B A3B Instruct 2507">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-30b-a3b-instruct-2507">Qwen: Qwen3 30B A3B Instruct 2507</a><code class="text-xs">qwen/qwen3-30b-a3b-instruct-2507</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,80</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Z.AI: GLM 4.5">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/z-ai/glm-4.5">Z.AI: GLM 4.5</a><code class="text-xs">z-ai/glm-4.5</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Z.AI: GLM 4.5 Air (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/z-ai/glm-4.5-air:free">Z.AI: GLM 4.5 Air (free)</a><code class="text-xs">z-ai/glm-4.5-air:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Z.AI: GLM 4.5 Air">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/z-ai/glm-4.5-air">Z.AI: GLM 4.5 Air</a><code class="text-xs">z-ai/glm-4.5-air</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$1,10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 235B A22B Thinking 2507">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-235b-a22b-thinking-2507">Qwen: Qwen3 235B A22B Thinking 2507</a><code class="text-xs">qwen/qwen3-235b-a22b-thinking-2507</code></td>
<td class="text-center">
<div>$0,078</div>
</td>
<td class="text-center">
<div>$0,312</div>
</td>
<td><span class="flex justify-center gap-1">262 144</span></td>
</tr>
<tr class="text-sm" title="Z.AI: GLM 4 32B ">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/z-ai/glm-4-32b">Z.AI: GLM 4 32B</a><code class="text-xs">z-ai/glm-4-32b</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 Coder  (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-coder:free">Qwen: Qwen3 Coder (free)</a><code class="text-xs">qwen/qwen3-coder:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">262 144</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 Coder ">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-coder">Qwen: Qwen3 Coder</a><code class="text-xs">qwen/qwen3-coder</code></td>
<td class="text-center">
<div>$0,30</div>
</td>
<td class="text-center">
<div>$1,20</div>
</td>
<td><span class="flex justify-center gap-1">262 144</span></td>
</tr>
<tr class="text-sm" title="Bytedance: UI-TARS 7B ">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/bytedance/ui-tars-1.5-7b">Bytedance: UI-TARS 7B</a><code class="text-xs">bytedance/ui-tars-1.5-7b</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.5 Flash Lite">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.5-flash-lite">Google: Gemini 2.5 Flash Lite</a><code class="text-xs">google/gemini-2.5-flash-lite</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 235B A22B Instruct 2507">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-235b-a22b-2507">Qwen: Qwen3 235B A22B Instruct 2507</a><code class="text-xs">qwen/qwen3-235b-a22b-2507</code></td>
<td class="text-center">
<div>$0,078</div>
</td>
<td class="text-center">
<div>$0,312</div>
</td>
<td><span class="flex justify-center gap-1">262 144</span></td>
</tr>
<tr class="text-sm" title="Switchpoint Router">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/switchpoint/router">Switchpoint Router</a><code class="text-xs">switchpoint/router</code></td>
<td class="text-center">
<div>$0,85</div>
</td>
<td class="text-center">
<div>$3,40</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="MoonshotAI: Kimi K2 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/moonshotai/kimi-k2:free">MoonshotAI: Kimi K2 (free)</a><code class="text-xs">moonshotai/kimi-k2:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="MoonshotAI: Kimi K2">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/moonshotai/kimi-k2">MoonshotAI: Kimi K2</a><code class="text-xs">moonshotai/kimi-k2</code></td>
<td class="text-center">
<div>$0,14</div>
</td>
<td class="text-center">
<div>$2,49</div>
</td>
<td><span class="flex justify-center gap-1">63 000</span></td>
</tr>
<tr class="text-sm" title="THUDM: GLM 4.1V 9B Thinking">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thudm/glm-4.1v-9b-thinking">THUDM: GLM 4.1V 9B Thinking</a><code class="text-xs">thudm/glm-4.1v-9b-thinking</code></td>
<td class="text-center">
<div>$0,035</div>
</td>
<td class="text-center">
<div>$0,138</div>
</td>
<td><span class="flex justify-center gap-1">65 536</span></td>
</tr>
<tr class="text-sm" title="Mistral: Devstral Medium">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/devstral-medium">Mistral: Devstral Medium</a><code class="text-xs">mistralai/devstral-medium</code></td>
<td class="text-center">
<div>$0,40</div>
</td>
<td class="text-center">
<div>$2</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Mistral: Devstral Small 1.1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/devstral-small">Mistral: Devstral Small 1.1</a><code class="text-xs">mistralai/devstral-small</code></td>
<td class="text-center">
<div>$0,07</div>
</td>
<td class="text-center">
<div>$0,28</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Venice: Uncensored (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cognitivecomputations/dolphin-mistral-24b-venice-edition:free">Venice: Uncensored (free)</a><code class="text-xs">cognitivecomputations/dolphin-mistral-24b-venice-edition:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="xAI: Grok 4">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/x-ai/grok-4">xAI: Grok 4</a><code class="text-xs">x-ai/grok-4</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">256 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3n 2B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3n-e2b-it:free">Google: Gemma 3n 2B (free)</a><code class="text-xs">google/gemma-3n-e2b-it:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Tencent: Hunyuan A13B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/tencent/hunyuan-a13b-instruct:free">Tencent: Hunyuan A13B Instruct (free)</a><code class="text-xs">tencent/hunyuan-a13b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Tencent: Hunyuan A13B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/tencent/hunyuan-a13b-instruct">Tencent: Hunyuan A13B Instruct</a><code class="text-xs">tencent/hunyuan-a13b-instruct</code></td>
<td class="text-center">
<div>$0,03</div>
</td>
<td class="text-center">
<div>$0,03</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="TNG: DeepSeek R1T2 Chimera (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/tngtech/deepseek-r1t2-chimera:free">TNG: DeepSeek R1T2 Chimera (free)</a><code class="text-xs">tngtech/deepseek-r1t2-chimera:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Morph: Morph V3 Large">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/morph/morph-v3-large">Morph: Morph V3 Large</a><code class="text-xs">morph/morph-v3-large</code></td>
<td class="text-center">
<div>$0,90</div>
</td>
<td class="text-center">
<div>$1,90</div>
</td>
<td><span class="flex justify-center gap-1">81 920</span></td>
</tr>
<tr class="text-sm" title="Morph: Morph V3 Fast">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/morph/morph-v3-fast">Morph: Morph V3 Fast</a><code class="text-xs">morph/morph-v3-fast</code></td>
<td class="text-center">
<div>$0,90</div>
</td>
<td class="text-center">
<div>$1,90</div>
</td>
<td><span class="flex justify-center gap-1">81 920</span></td>
</tr>
<tr class="text-sm" title="Baidu: ERNIE 4.5 300B A47B ">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/baidu/ernie-4.5-300b-a47b">Baidu: ERNIE 4.5 300B A47B</a><code class="text-xs">baidu/ernie-4.5-300b-a47b</code></td>
<td class="text-center">
<div>$0,28</div>
</td>
<td class="text-center">
<div>$1,10</div>
</td>
<td><span class="flex justify-center gap-1">123 000</span></td>
</tr>
<tr class="text-sm" title="TheDrummer: Anubis 70B V1.1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thedrummer/anubis-70b-v1.1">TheDrummer: Anubis 70B V1.1</a><code class="text-xs">thedrummer/anubis-70b-v1.1</code></td>
<td class="text-center">
<div>$0,40</div>
</td>
<td class="text-center">
<div>$0,70</div>
</td>
<td><span class="flex justify-center gap-1">16 384</span></td>
</tr>
<tr class="text-sm" title="Inception: Mercury">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/inception/mercury">Inception: Mercury</a><code class="text-xs">inception/mercury</code></td>
<td class="text-center">
<div>$0,25</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Small 3.2 24B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct:free">Mistral: Mistral Small 3.2 24B (free)</a><code class="text-xs">mistralai/mistral-small-3.2-24b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Small 3.2 24B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct">Mistral: Mistral Small 3.2 24B</a><code class="text-xs">mistralai/mistral-small-3.2-24b-instruct</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,08</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="MiniMax: MiniMax M1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/minimax/minimax-m1">MiniMax: MiniMax M1</a><code class="text-xs">minimax/minimax-m1</code></td>
<td class="text-center">
<div>$0,30
<div class="font-medium text-green-600">25% off</div>
</div>
</td>
<td class="text-center">
<div>$1,65
<div class="font-medium text-green-600">25% off</div>
</div>
</td>
<td><span class="flex justify-center gap-1">1 000 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.5 Flash Lite Preview 06-17">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.5-flash-lite-preview-06-17">Google: Gemini 2.5 Flash Lite Preview 06-17</a><code class="text-xs">google/gemini-2.5-flash-lite-preview-06-17</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.5 Flash">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.5-flash">Google: Gemini 2.5 Flash</a><code class="text-xs">google/gemini-2.5-flash</code></td>
<td class="text-center">
<div>$0,30</div>
</td>
<td class="text-center">
<div>$2,50</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.5 Pro">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.5-pro">Google: Gemini 2.5 Pro</a><code class="text-xs">google/gemini-2.5-pro</code></td>
<td class="text-center">
<div>$1,25</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Kimi Dev 72b (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/moonshotai/kimi-dev-72b:free">Kimi Dev 72b (free)</a><code class="text-xs">moonshotai/kimi-dev-72b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o3 Pro">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o3-pro">OpenAI: o3 Pro</a><code class="text-xs">openai/o3-pro</code></td>
<td class="text-center">
<div>$20</div>
</td>
<td class="text-center">
<div>$80</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="xAI: Grok 3 Mini">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/x-ai/grok-3-mini">xAI: Grok 3 Mini</a><code class="text-xs">x-ai/grok-3-mini</code></td>
<td class="text-center">
<div>$0,30</div>
</td>
<td class="text-center">
<div>$0,50</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="xAI: Grok 3">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/x-ai/grok-3">xAI: Grok 3</a><code class="text-xs">x-ai/grok-3</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Mistral: Magistral Small 2506">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/magistral-small-2506">Mistral: Magistral Small 2506</a><code class="text-xs">mistralai/magistral-small-2506</code></td>
<td class="text-center">
<div>$0,50</div>
</td>
<td class="text-center">
<div>$1,50</div>
</td>
<td><span class="flex justify-center gap-1">40 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Magistral Medium 2506">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/magistral-medium-2506">Mistral: Magistral Medium 2506</a><code class="text-xs">mistralai/magistral-medium-2506</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$5</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="Mistral: Magistral Medium 2506 (thinking)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/magistral-medium-2506:thinking">Mistral: Magistral Medium 2506 (thinking)</a><code class="text-xs">mistralai/magistral-medium-2506:thinking</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$5</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.5 Pro Preview 06-05">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.5-pro-preview">Google: Gemini 2.5 Pro Preview 06-05</a><code class="text-xs">google/gemini-2.5-pro-preview</code></td>
<td class="text-center">
<div>$1,25</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 Distill Qwen 7B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-distill-qwen-7b">DeepSeek: R1 Distill Qwen 7B</a><code class="text-xs">deepseek/deepseek-r1-distill-qwen-7b</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: Deepseek R1 0528 Qwen3 8B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-0528-qwen3-8b:free">DeepSeek: Deepseek R1 0528 Qwen3 8B (free)</a><code class="text-xs">deepseek/deepseek-r1-0528-qwen3-8b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: Deepseek R1 0528 Qwen3 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-0528-qwen3-8b">DeepSeek: Deepseek R1 0528 Qwen3 8B</a><code class="text-xs">deepseek/deepseek-r1-0528-qwen3-8b</code></td>
<td class="text-center">
<div>$0,01</div>
</td>
<td class="text-center">
<div>$0,02</div>
</td>
<td><span class="flex justify-center gap-1">32 000</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 0528 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-0528:free">DeepSeek: R1 0528 (free)</a><code class="text-xs">deepseek/deepseek-r1-0528:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 0528">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-0528">DeepSeek: R1 0528</a><code class="text-xs">deepseek/deepseek-r1-0528</code></td>
<td class="text-center">
<div>$0,272</div>
</td>
<td class="text-center">
<div>$0,272</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Sarvam AI: Sarvam-M (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/sarvamai/sarvam-m:free">Sarvam AI: Sarvam-M (free)</a><code class="text-xs">sarvamai/sarvam-m:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="TheDrummer: Valkyrie 49B V1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thedrummer/valkyrie-49b-v1">TheDrummer: Valkyrie 49B V1</a><code class="text-xs">thedrummer/valkyrie-49b-v1</code></td>
<td class="text-center">
<div>$0,65</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude Opus 4">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-opus-4">Anthropic: Claude Opus 4</a><code class="text-xs">anthropic/claude-opus-4</code></td>
<td class="text-center">
<div>$15</div>
</td>
<td class="text-center">
<div>$75</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude Sonnet 4">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-sonnet-4">Anthropic: Claude Sonnet 4</a><code class="text-xs">anthropic/claude-sonnet-4</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Devstral Small 2505 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/devstral-small-2505:free">Mistral: Devstral Small 2505 (free)</a><code class="text-xs">mistralai/devstral-small-2505:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral: Devstral Small 2505">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/devstral-small-2505">Mistral: Devstral Small 2505</a><code class="text-xs">mistralai/devstral-small-2505</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,08</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3n 4B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3n-e4b-it:free">Google: Gemma 3n 4B (free)</a><code class="text-xs">google/gemma-3n-e4b-it:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3n 4B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3n-e4b-it">Google: Gemma 3n 4B</a><code class="text-xs">google/gemma-3n-e4b-it</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,04</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="OpenAI: Codex Mini">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/codex-mini">OpenAI: Codex Mini</a><code class="text-xs">openai/codex-mini</code></td>
<td class="text-center">
<div>$1,50</div>
</td>
<td class="text-center">
<div>$6</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Nous: DeepHermes 3 Mistral 24B Preview">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nousresearch/deephermes-3-mistral-24b-preview">Nous: DeepHermes 3 Mistral 24B Preview</a><code class="text-xs">nousresearch/deephermes-3-mistral-24b-preview</code></td>
<td class="text-center">
<div>$0,093</div>
</td>
<td class="text-center">
<div>$0,373</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Medium 3">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-medium-3">Mistral: Mistral Medium 3</a><code class="text-xs">mistralai/mistral-medium-3</code></td>
<td class="text-center">
<div>$0,40</div>
</td>
<td class="text-center">
<div>$2</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.5 Pro Preview 05-06">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.5-pro-preview-05-06">Google: Gemini 2.5 Pro Preview 05-06</a><code class="text-xs">google/gemini-2.5-pro-preview-05-06</code></td>
<td class="text-center">
<div>$1,25</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Arcee AI: Spotlight">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/arcee-ai/spotlight">Arcee AI: Spotlight</a><code class="text-xs">arcee-ai/spotlight</code></td>
<td class="text-center">
<div>$0,18</div>
</td>
<td class="text-center">
<div>$0,18</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Arcee AI: Maestro Reasoning">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/arcee-ai/maestro-reasoning">Arcee AI: Maestro Reasoning</a><code class="text-xs">arcee-ai/maestro-reasoning</code></td>
<td class="text-center">
<div>$0,90</div>
</td>
<td class="text-center">
<div>$3,30</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Arcee AI: Virtuoso Large">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/arcee-ai/virtuoso-large">Arcee AI: Virtuoso Large</a><code class="text-xs">arcee-ai/virtuoso-large</code></td>
<td class="text-center">
<div>$0,75</div>
</td>
<td class="text-center">
<div>$1,20</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Arcee AI: Coder Large">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/arcee-ai/coder-large">Arcee AI: Coder Large</a><code class="text-xs">arcee-ai/coder-large</code></td>
<td class="text-center">
<div>$0,50</div>
</td>
<td class="text-center">
<div>$0,80</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Microsoft: Phi 4 Reasoning Plus">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/phi-4-reasoning-plus">Microsoft: Phi 4 Reasoning Plus</a><code class="text-xs">microsoft/phi-4-reasoning-plus</code></td>
<td class="text-center">
<div>$0,07</div>
</td>
<td class="text-center">
<div>$0,35</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Inception: Mercury Coder">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/inception/mercury-coder">Inception: Mercury Coder</a><code class="text-xs">inception/mercury-coder</code></td>
<td class="text-center">
<div>$0,25</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 4B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-4b:free">Qwen: Qwen3 4B (free)</a><code class="text-xs">qwen/qwen3-4b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="OpenGVLab: InternVL3 14B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/opengvlab/internvl3-14b">OpenGVLab: InternVL3 14B</a><code class="text-xs">opengvlab/internvl3-14b</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">12 288</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: DeepSeek Prover V2">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-prover-v2">DeepSeek: DeepSeek Prover V2</a><code class="text-xs">deepseek/deepseek-prover-v2</code></td>
<td class="text-center">
<div>$0,50</div>
</td>
<td class="text-center">
<div>$2,18</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama Guard 4 12B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-guard-4-12b">Meta: Llama Guard 4 12B</a><code class="text-xs">meta-llama/llama-guard-4-12b</code></td>
<td class="text-center">
<div>$0,05</div>
</td>
<td class="text-center">
<div>$0,05</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 30B A3B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-30b-a3b:free">Qwen: Qwen3 30B A3B (free)</a><code class="text-xs">qwen/qwen3-30b-a3b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 30B A3B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-30b-a3b">Qwen: Qwen3 30B A3B</a><code class="text-xs">qwen/qwen3-30b-a3b</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,08</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 8B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-8b:free">Qwen: Qwen3 8B (free)</a><code class="text-xs">qwen/qwen3-8b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-8b">Qwen: Qwen3 8B</a><code class="text-xs">qwen/qwen3-8b</code></td>
<td class="text-center">
<div>$0,035</div>
</td>
<td class="text-center">
<div>$0,138</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 14B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-14b:free">Qwen: Qwen3 14B (free)</a><code class="text-xs">qwen/qwen3-14b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 14B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-14b">Qwen: Qwen3 14B</a><code class="text-xs">qwen/qwen3-14b</code></td>
<td class="text-center">
<div>$0,06</div>
</td>
<td class="text-center">
<div>$0,24</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 32B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-32b">Qwen: Qwen3 32B</a><code class="text-xs">qwen/qwen3-32b</code></td>
<td class="text-center">
<div>$0,018</div>
</td>
<td class="text-center">
<div>$0,072</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 235B A22B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-235b-a22b:free">Qwen: Qwen3 235B A22B (free)</a><code class="text-xs">qwen/qwen3-235b-a22b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen3 235B A22B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen3-235b-a22b">Qwen: Qwen3 235B A22B</a><code class="text-xs">qwen/qwen3-235b-a22b</code></td>
<td class="text-center">
<div>$0,13</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">40 960</span></td>
</tr>
<tr class="text-sm" title="TNG: DeepSeek R1T Chimera (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/tngtech/deepseek-r1t-chimera:free">TNG: DeepSeek R1T Chimera (free)</a><code class="text-xs">tngtech/deepseek-r1t-chimera:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="TNG: DeepSeek R1T Chimera">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/tngtech/deepseek-r1t-chimera">TNG: DeepSeek R1T Chimera</a><code class="text-xs">tngtech/deepseek-r1t-chimera</code></td>
<td class="text-center">
<div>$0,18</div>
</td>
<td class="text-center">
<div>$0,72</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Microsoft: MAI DS R1 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/mai-ds-r1:free">Microsoft: MAI DS R1 (free)</a><code class="text-xs">microsoft/mai-ds-r1:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Microsoft: MAI DS R1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/mai-ds-r1">Microsoft: MAI DS R1</a><code class="text-xs">microsoft/mai-ds-r1</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,80</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="THUDM: GLM Z1 32B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thudm/glm-z1-32b:free">THUDM: GLM Z1 32B (free)</a><code class="text-xs">thudm/glm-z1-32b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="THUDM: GLM 4 32B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thudm/glm-4-32b">THUDM: GLM 4 32B</a><code class="text-xs">thudm/glm-4-32b</code></td>
<td class="text-center">
<div>$0,24</div>
</td>
<td class="text-center">
<div>$0,24</div>
</td>
<td><span class="flex justify-center gap-1">32 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o4 Mini High">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o4-mini-high">OpenAI: o4 Mini High</a><code class="text-xs">openai/o4-mini-high</code></td>
<td class="text-center">
<div>$1,10</div>
</td>
<td class="text-center">
<div>$4,40</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o3">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o3">OpenAI: o3</a><code class="text-xs">openai/o3</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$8</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o4 Mini">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o4-mini">OpenAI: o4 Mini</a><code class="text-xs">openai/o4-mini</code></td>
<td class="text-center">
<div>$1,10</div>
</td>
<td class="text-center">
<div>$4,40</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Shisa AI: Shisa V2 Llama 3.3 70B  (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/shisa-ai/shisa-v2-llama3.3-70b:free">Shisa AI: Shisa V2 Llama 3.3 70B (free)</a><code class="text-xs">shisa-ai/shisa-v2-llama3.3-70b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Shisa AI: Shisa V2 Llama 3.3 70B ">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/shisa-ai/shisa-v2-llama3.3-70b">Shisa AI: Shisa V2 Llama 3.3 70B</a><code class="text-xs">shisa-ai/shisa-v2-llama3.3-70b</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,08</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4.1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4.1">OpenAI: GPT-4.1</a><code class="text-xs">openai/gpt-4.1</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$8</div>
</td>
<td><span class="flex justify-center gap-1">1 047 576</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4.1 Mini">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4.1-mini">OpenAI: GPT-4.1 Mini</a><code class="text-xs">openai/gpt-4.1-mini</code></td>
<td class="text-center">
<div>$0,40</div>
</td>
<td class="text-center">
<div>$1,60</div>
</td>
<td><span class="flex justify-center gap-1">1 047 576</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4.1 Nano">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4.1-nano">OpenAI: GPT-4.1 Nano</a><code class="text-xs">openai/gpt-4.1-nano</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">1 047 576</span></td>
</tr>
<tr class="text-sm" title="EleutherAI: Llemma 7b">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/eleutherai/llemma_7b">EleutherAI: Llemma 7b</a><code class="text-xs">eleutherai/llemma_7b</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$1,20</div>
</td>
<td><span class="flex justify-center gap-1">4 096</span></td>
</tr>
<tr class="text-sm" title="AlfredPros: CodeLLaMa 7B Instruct Solidity">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/alfredpros/codellama-7b-instruct-solidity">AlfredPros: CodeLLaMa 7B Instruct Solidity</a><code class="text-xs">alfredpros/codellama-7b-instruct-solidity</code></td>
<td class="text-center">
<div>$0,60</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="ArliAI: QwQ 32B RpR v1 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/arliai/qwq-32b-arliai-rpr-v1:free">ArliAI: QwQ 32B RpR v1 (free)</a><code class="text-xs">arliai/qwq-32b-arliai-rpr-v1:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="ArliAI: QwQ 32B RpR v1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/arliai/qwq-32b-arliai-rpr-v1">ArliAI: QwQ 32B RpR v1</a><code class="text-xs">arliai/qwq-32b-arliai-rpr-v1</code></td>
<td class="text-center">
<div>$0,01</div>
</td>
<td class="text-center">
<div>$0,04</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Agentica: Deepcoder 14B Preview (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/agentica-org/deepcoder-14b-preview:free">Agentica: Deepcoder 14B Preview (free)</a><code class="text-xs">agentica-org/deepcoder-14b-preview:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">96 000</span></td>
</tr>
<tr class="text-sm" title="Agentica: Deepcoder 14B Preview">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/agentica-org/deepcoder-14b-preview">Agentica: Deepcoder 14B Preview</a><code class="text-xs">agentica-org/deepcoder-14b-preview</code></td>
<td class="text-center">
<div>$0,015</div>
</td>
<td class="text-center">
<div>$0,015</div>
</td>
<td><span class="flex justify-center gap-1">96 000</span></td>
</tr>
<tr class="text-sm" title="Moonshot AI: Kimi VL A3B Thinking (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/moonshotai/kimi-vl-a3b-thinking:free">Moonshot AI: Kimi VL A3B Thinking (free)</a><code class="text-xs">moonshotai/kimi-vl-a3b-thinking:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Moonshot AI: Kimi VL A3B Thinking">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/moonshotai/kimi-vl-a3b-thinking">Moonshot AI: Kimi VL A3B Thinking</a><code class="text-xs">moonshotai/kimi-vl-a3b-thinking</code></td>
<td class="text-center">
<div>$0,025</div>
</td>
<td class="text-center">
<div>$0,10</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="xAI: Grok 3 Mini Beta">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/x-ai/grok-3-mini-beta">xAI: Grok 3 Mini Beta</a><code class="text-xs">x-ai/grok-3-mini-beta</code></td>
<td class="text-center">
<div>$0,30</div>
</td>
<td class="text-center">
<div>$0,50</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="xAI: Grok 3 Beta">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/x-ai/grok-3-beta">xAI: Grok 3 Beta</a><code class="text-xs">x-ai/grok-3-beta</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="NVIDIA: Llama 3.3 Nemotron Super 49B v1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nvidia/llama-3.3-nemotron-super-49b-v1">NVIDIA: Llama 3.3 Nemotron Super 49B v1</a><code class="text-xs">nvidia/llama-3.3-nemotron-super-49b-v1</code></td>
<td class="text-center">
<div>$0,13</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="NVIDIA: Llama 3.1 Nemotron Ultra 253B v1 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1:free">NVIDIA: Llama 3.1 Nemotron Ultra 253B v1 (free)</a><code class="text-xs">nvidia/llama-3.1-nemotron-ultra-253b-v1:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="NVIDIA: Llama 3.1 Nemotron Ultra 253B v1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nvidia/llama-3.1-nemotron-ultra-253b-v1">NVIDIA: Llama 3.1 Nemotron Ultra 253B v1</a><code class="text-xs">nvidia/llama-3.1-nemotron-ultra-253b-v1</code></td>
<td class="text-center">
<div>$0,60</div>
</td>
<td class="text-center">
<div>$1,80</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 4 Maverick">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-4-maverick">Meta: Llama 4 Maverick</a><code class="text-xs">meta-llama/llama-4-maverick</code></td>
<td class="text-center">
<div>$0,15</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 4 Scout">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-4-scout">Meta: Llama 4 Scout</a><code class="text-xs">meta-llama/llama-4-scout</code></td>
<td class="text-center">
<div>$0,08</div>
</td>
<td class="text-center">
<div>$0,30</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: DeepSeek V3 Base">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-v3-base">DeepSeek: DeepSeek V3 Base</a><code class="text-xs">deepseek/deepseek-v3-base</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,80</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Typhoon2 70B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/scb10x/llama3.1-typhoon2-70b-instruct">Typhoon2 70B Instruct</a><code class="text-xs">scb10x/llama3.1-typhoon2-70b-instruct</code></td>
<td class="text-center">
<div>$0,88</div>
</td>
<td class="text-center">
<div>$0,88</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.5 Pro Experimental">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.5-pro-exp-03-25">Google: Gemini 2.5 Pro Experimental</a><code class="text-xs">google/gemini-2.5-pro-exp-03-25</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen2.5 VL 32B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen2.5-vl-32b-instruct:free">Qwen: Qwen2.5 VL 32B Instruct (free)</a><code class="text-xs">qwen/qwen2.5-vl-32b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen2.5 VL 32B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen2.5-vl-32b-instruct">Qwen: Qwen2.5 VL 32B Instruct</a><code class="text-xs">qwen/qwen2.5-vl-32b-instruct</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,08</div>
</td>
<td><span class="flex justify-center gap-1">16 384</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: DeepSeek V3 0324 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-chat-v3-0324:free">DeepSeek: DeepSeek V3 0324 (free)</a><code class="text-xs">deepseek/deepseek-chat-v3-0324:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: DeepSeek V3 0324">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-chat-v3-0324">DeepSeek: DeepSeek V3 0324</a><code class="text-xs">deepseek/deepseek-chat-v3-0324</code></td>
<td class="text-center">
<div>$0,25</div>
</td>
<td class="text-center">
<div>$0,85</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Qrwkv 72B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/featherless/qwerky-72b:free">Qrwkv 72B (free)</a><code class="text-xs">featherless/qwerky-72b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o1-pro">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o1-pro">OpenAI: o1-pro</a><code class="text-xs">openai/o1-pro</code></td>
<td class="text-center">
<div>$150</div>
</td>
<td class="text-center">
<div>$600</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Small 3.1 24B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-small-3.1-24b-instruct:free">Mistral: Mistral Small 3.1 24B (free)</a><code class="text-xs">mistralai/mistral-small-3.1-24b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Small 3.1 24B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-small-3.1-24b-instruct">Mistral: Mistral Small 3.1 24B</a><code class="text-xs">mistralai/mistral-small-3.1-24b-instruct</code></td>
<td class="text-center">
<div>$0,018</div>
</td>
<td class="text-center">
<div>$0,072</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3 4B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3-4b-it:free">Google: Gemma 3 4B (free)</a><code class="text-xs">google/gemma-3-4b-it:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3 4B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3-4b-it">Google: Gemma 3 4B</a><code class="text-xs">google/gemma-3-4b-it</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,04</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="AI21: Jamba 1.6 Large">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/ai21/jamba-1.6-large">AI21: Jamba 1.6 Large</a><code class="text-xs">ai21/jamba-1.6-large</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$8</div>
</td>
<td><span class="flex justify-center gap-1">256 000</span></td>
</tr>
<tr class="text-sm" title="AI21: Jamba Mini 1.6">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/ai21/jamba-1.6-mini">AI21: Jamba Mini 1.6</a><code class="text-xs">ai21/jamba-1.6-mini</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">256 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3 12B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3-12b-it:free">Google: Gemma 3 12B (free)</a><code class="text-xs">google/gemma-3-12b-it:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">96 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3 12B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3-12b-it">Google: Gemma 3 12B</a><code class="text-xs">google/gemma-3-12b-it</code></td>
<td class="text-center">
<div>$0,048</div>
</td>
<td class="text-center">
<div>$0,193</div>
</td>
<td><span class="flex justify-center gap-1">96 000</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command A">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command-a">Cohere: Command A</a><code class="text-xs">cohere/command-a</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$8</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o-mini Search Preview">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o-mini-search-preview">OpenAI: GPT-4o-mini Search Preview</a><code class="text-xs">openai/gpt-4o-mini-search-preview</code></td>
<td class="text-center">
<div>$0,15</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o Search Preview">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o-search-preview">OpenAI: GPT-4o Search Preview</a><code class="text-xs">openai/gpt-4o-search-preview</code></td>
<td class="text-center">
<div>$2,50</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Reka: Flash 3 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/rekaai/reka-flash-3:free">Reka: Flash 3 (free)</a><code class="text-xs">rekaai/reka-flash-3:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3 27B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3-27b-it:free">Google: Gemma 3 27B (free)</a><code class="text-xs">google/gemma-3-27b-it:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">96 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 3 27B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-3-27b-it">Google: Gemma 3 27B</a><code class="text-xs">google/gemma-3-27b-it</code></td>
<td class="text-center">
<div>$0,067</div>
</td>
<td class="text-center">
<div>$0,267</div>
</td>
<td><span class="flex justify-center gap-1">96 000</span></td>
</tr>
<tr class="text-sm" title="TheDrummer: Anubis Pro 105B V1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thedrummer/anubis-pro-105b-v1">TheDrummer: Anubis Pro 105B V1</a><code class="text-xs">thedrummer/anubis-pro-105b-v1</code></td>
<td class="text-center">
<div>$0,50</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="TheDrummer: Skyfall 36B V2">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thedrummer/skyfall-36b-v2">TheDrummer: Skyfall 36B V2</a><code class="text-xs">thedrummer/skyfall-36b-v2</code></td>
<td class="text-center">
<div>$0,048</div>
</td>
<td class="text-center">
<div>$0,193</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Microsoft: Phi 4 Multimodal Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/phi-4-multimodal-instruct">Microsoft: Phi 4 Multimodal Instruct</a><code class="text-xs">microsoft/phi-4-multimodal-instruct</code></td>
<td class="text-center">
<div>$0,05</div>
</td>
<td class="text-center">
<div>$0,10</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Perplexity: Sonar Reasoning Pro">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/perplexity/sonar-reasoning-pro">Perplexity: Sonar Reasoning Pro</a><code class="text-xs">perplexity/sonar-reasoning-pro</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$8</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Perplexity: Sonar Pro">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/perplexity/sonar-pro">Perplexity: Sonar Pro</a><code class="text-xs">perplexity/sonar-pro</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Perplexity: Sonar Deep Research">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/perplexity/sonar-deep-research">Perplexity: Sonar Deep Research</a><code class="text-xs">perplexity/sonar-deep-research</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$8</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: QwQ 32B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwq-32b:free">Qwen: QwQ 32B (free)</a><code class="text-xs">qwen/qwq-32b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Qwen: QwQ 32B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwq-32b">Qwen: QwQ 32B</a><code class="text-xs">qwen/qwq-32b</code></td>
<td class="text-center">
<div>$0,075</div>
</td>
<td class="text-center">
<div>$0,15</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Nous: DeepHermes 3 Llama 3 8B Preview (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nousresearch/deephermes-3-llama-3-8b-preview:free">Nous: DeepHermes 3 Llama 3 8B Preview (free)</a><code class="text-xs">nousresearch/deephermes-3-llama-3-8b-preview:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.0 Flash Lite">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.0-flash-lite-001">Google: Gemini 2.0 Flash Lite</a><code class="text-xs">google/gemini-2.0-flash-lite-001</code></td>
<td class="text-center">
<div>$0,075</div>
</td>
<td class="text-center">
<div>$0,30</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.7 Sonnet">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.7-sonnet">Anthropic: Claude 3.7 Sonnet</a><code class="text-xs">anthropic/claude-3.7-sonnet</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.7 Sonnet (thinking)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.7-sonnet:thinking">Anthropic: Claude 3.7 Sonnet (thinking)</a><code class="text-xs">anthropic/claude-3.7-sonnet:thinking</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.7 Sonnet (self-moderated)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.7-sonnet:beta">Anthropic: Claude 3.7 Sonnet (self-moderated)</a><code class="text-xs">anthropic/claude-3.7-sonnet:beta</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Perplexity: R1 1776">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/perplexity/r1-1776">Perplexity: R1 1776</a><code class="text-xs">perplexity/r1-1776</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$8</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Saba">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-saba">Mistral: Saba</a><code class="text-xs">mistralai/mistral-saba</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Dolphin3.0 R1 Mistral 24B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cognitivecomputations/dolphin3.0-r1-mistral-24b:free">Dolphin3.0 R1 Mistral 24B (free)</a><code class="text-xs">cognitivecomputations/dolphin3.0-r1-mistral-24b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Dolphin3.0 R1 Mistral 24B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cognitivecomputations/dolphin3.0-r1-mistral-24b">Dolphin3.0 R1 Mistral 24B</a><code class="text-xs">cognitivecomputations/dolphin3.0-r1-mistral-24b</code></td>
<td class="text-center">
<div>$0,01</div>
</td>
<td class="text-center">
<div>$0,034</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Dolphin3.0 Mistral 24B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cognitivecomputations/dolphin3.0-mistral-24b:free">Dolphin3.0 Mistral 24B (free)</a><code class="text-xs">cognitivecomputations/dolphin3.0-mistral-24b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Dolphin3.0 Mistral 24B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cognitivecomputations/dolphin3.0-mistral-24b">Dolphin3.0 Mistral 24B</a><code class="text-xs">cognitivecomputations/dolphin3.0-mistral-24b</code></td>
<td class="text-center">
<div>$0,037</div>
</td>
<td class="text-center">
<div>$0,148</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Llama Guard 3 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-guard-3-8b">Llama Guard 3 8B</a><code class="text-xs">meta-llama/llama-guard-3-8b</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,06</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o3 Mini High">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o3-mini-high">OpenAI: o3 Mini High</a><code class="text-xs">openai/o3-mini-high</code></td>
<td class="text-center">
<div>$1,10</div>
</td>
<td class="text-center">
<div>$4,40</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 Distill Llama 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-distill-llama-8b">DeepSeek: R1 Distill Llama 8B</a><code class="text-xs">deepseek/deepseek-r1-distill-llama-8b</code></td>
<td class="text-center">
<div>$0,04</div>
</td>
<td class="text-center">
<div>$0,04</div>
</td>
<td><span class="flex justify-center gap-1">32 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.0 Flash">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.0-flash-001">Google: Gemini 2.0 Flash</a><code class="text-xs">google/gemini-2.0-flash-001</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen VL Plus">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-vl-plus">Qwen: Qwen VL Plus</a><code class="text-xs">qwen/qwen-vl-plus</code></td>
<td class="text-center">
<div>$0,21</div>
</td>
<td class="text-center">
<div>$0,63</div>
</td>
<td><span class="flex justify-center gap-1">7 500</span></td>
</tr>
<tr class="text-sm" title="AionLabs: Aion-1.0">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/aion-labs/aion-1.0">AionLabs: Aion-1.0</a><code class="text-xs">aion-labs/aion-1.0</code></td>
<td class="text-center">
<div>$4</div>
</td>
<td class="text-center">
<div>$8</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="AionLabs: Aion-1.0-Mini">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/aion-labs/aion-1.0-mini">AionLabs: Aion-1.0-Mini</a><code class="text-xs">aion-labs/aion-1.0-mini</code></td>
<td class="text-center">
<div>$0,70</div>
</td>
<td class="text-center">
<div>$1,40</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="AionLabs: Aion-RP 1.0 (8B)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/aion-labs/aion-rp-llama-3.1-8b">AionLabs: Aion-RP 1.0 (8B)</a><code class="text-xs">aion-labs/aion-rp-llama-3.1-8b</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen VL Max">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-vl-max">Qwen: Qwen VL Max</a><code class="text-xs">qwen/qwen-vl-max</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$3,20</div>
</td>
<td><span class="flex justify-center gap-1">7 500</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen-Turbo">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-turbo">Qwen: Qwen-Turbo</a><code class="text-xs">qwen/qwen-turbo</code></td>
<td class="text-center">
<div>$0,05</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">1 000 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen2.5 VL 72B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen2.5-vl-72b-instruct:free">Qwen: Qwen2.5 VL 72B Instruct (free)</a><code class="text-xs">qwen/qwen2.5-vl-72b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen2.5 VL 72B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen2.5-vl-72b-instruct">Qwen: Qwen2.5 VL 72B Instruct</a><code class="text-xs">qwen/qwen2.5-vl-72b-instruct</code></td>
<td class="text-center">
<div>$0,25</div>
</td>
<td class="text-center">
<div>$0,75</div>
</td>
<td><span class="flex justify-center gap-1">32 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen-Plus">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-plus">Qwen: Qwen-Plus</a><code class="text-xs">qwen/qwen-plus</code></td>
<td class="text-center">
<div>$0,40</div>
</td>
<td class="text-center">
<div>$1,20</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen-Max ">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-max">Qwen: Qwen-Max</a><code class="text-xs">qwen/qwen-max</code></td>
<td class="text-center">
<div>$1,60</div>
</td>
<td class="text-center">
<div>$6,40</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o3 Mini">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o3-mini">OpenAI: o3 Mini</a><code class="text-xs">openai/o3-mini</code></td>
<td class="text-center">
<div>$1,10</div>
</td>
<td class="text-center">
<div>$4,40</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 Distill Qwen 1.5B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-distill-qwen-1.5b">DeepSeek: R1 Distill Qwen 1.5B</a><code class="text-xs">deepseek/deepseek-r1-distill-qwen-1.5b</code></td>
<td class="text-center">
<div>$0,18</div>
</td>
<td class="text-center">
<div>$0,18</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Small 3 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-small-24b-instruct-2501:free">Mistral: Mistral Small 3 (free)</a><code class="text-xs">mistralai/mistral-small-24b-instruct-2501:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Small 3">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-small-24b-instruct-2501">Mistral: Mistral Small 3</a><code class="text-xs">mistralai/mistral-small-24b-instruct-2501</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,08</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 Distill Qwen 32B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-distill-qwen-32b">DeepSeek: R1 Distill Qwen 32B</a><code class="text-xs">deepseek/deepseek-r1-distill-qwen-32b</code></td>
<td class="text-center">
<div>$0,075</div>
</td>
<td class="text-center">
<div>$0,15</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 Distill Qwen 14B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-distill-qwen-14b:free">DeepSeek: R1 Distill Qwen 14B (free)</a><code class="text-xs">deepseek/deepseek-r1-distill-qwen-14b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">64 000</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 Distill Qwen 14B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-distill-qwen-14b">DeepSeek: R1 Distill Qwen 14B</a><code class="text-xs">deepseek/deepseek-r1-distill-qwen-14b</code></td>
<td class="text-center">
<div>$0,15</div>
</td>
<td class="text-center">
<div>$0,15</div>
</td>
<td><span class="flex justify-center gap-1">64 000</span></td>
</tr>
<tr class="text-sm" title="Perplexity: Sonar Reasoning">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/perplexity/sonar-reasoning">Perplexity: Sonar Reasoning</a><code class="text-xs">perplexity/sonar-reasoning</code></td>
<td class="text-center">
<div>$1</div>
</td>
<td class="text-center">
<div>$5</div>
</td>
<td><span class="flex justify-center gap-1">127 000</span></td>
</tr>
<tr class="text-sm" title="Perplexity: Sonar">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/perplexity/sonar">Perplexity: Sonar</a><code class="text-xs">perplexity/sonar</code></td>
<td class="text-center">
<div>$1</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">127 072</span></td>
</tr>
<tr class="text-sm" title="Liquid: LFM 7B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/liquid/lfm-7b">Liquid: LFM 7B</a><code class="text-xs">liquid/lfm-7b</code></td>
<td class="text-center">
<div>$0,01</div>
</td>
<td class="text-center">
<div>$0,01</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Liquid: LFM 3B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/liquid/lfm-3b">Liquid: LFM 3B</a><code class="text-xs">liquid/lfm-3b</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,02</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 Distill Llama 70B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-distill-llama-70b:free">DeepSeek: R1 Distill Llama 70B (free)</a><code class="text-xs">deepseek/deepseek-r1-distill-llama-70b:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 Distill Llama 70B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1-distill-llama-70b">DeepSeek: R1 Distill Llama 70B</a><code class="text-xs">deepseek/deepseek-r1-distill-llama-70b</code></td>
<td class="text-center">
<div>$0,033</div>
</td>
<td class="text-center">
<div>$0,133</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1 (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1:free">DeepSeek: R1 (free)</a><code class="text-xs">deepseek/deepseek-r1:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: R1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-r1">DeepSeek: R1</a><code class="text-xs">deepseek/deepseek-r1</code></td>
<td class="text-center">
<div>$0,40</div>
</td>
<td class="text-center">
<div>$2</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="MiniMax: MiniMax-01">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/minimax/minimax-01">MiniMax: MiniMax-01</a><code class="text-xs">minimax/minimax-01</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$1,10</div>
</td>
<td><span class="flex justify-center gap-1">1 000 192</span></td>
</tr>
<tr class="text-sm" title="Mistral: Codestral 2501">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/codestral-2501">Mistral: Codestral 2501</a><code class="text-xs">mistralai/codestral-2501</code></td>
<td class="text-center">
<div>$0,30</div>
</td>
<td class="text-center">
<div>$0,90</div>
</td>
<td><span class="flex justify-center gap-1">262 144</span></td>
</tr>
<tr class="text-sm" title="Microsoft: Phi 4">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/phi-4">Microsoft: Phi 4</a><code class="text-xs">microsoft/phi-4</code></td>
<td class="text-center">
<div>$0,06</div>
</td>
<td class="text-center">
<div>$0,14</div>
</td>
<td><span class="flex justify-center gap-1">16 384</span></td>
</tr>
<tr class="text-sm" title="DeepSeek: DeepSeek V3">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/deepseek/deepseek-chat">DeepSeek: DeepSeek V3</a><code class="text-xs">deepseek/deepseek-chat</code></td>
<td class="text-center">
<div>$0,18</div>
</td>
<td class="text-center">
<div>$0,72</div>
</td>
<td><span class="flex justify-center gap-1">163 840</span></td>
</tr>
<tr class="text-sm" title="Sao10K: Llama 3.3 Euryale 70B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/sao10k/l3.3-euryale-70b">Sao10K: Llama 3.3 Euryale 70B</a><code class="text-xs">sao10k/l3.3-euryale-70b</code></td>
<td class="text-center">
<div>$0,65</div>
</td>
<td class="text-center">
<div>$0,75</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o1">OpenAI: o1</a><code class="text-xs">openai/o1</code></td>
<td class="text-center">
<div>$15</div>
</td>
<td class="text-center">
<div>$60</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="xAI: Grok 2 Vision 1212">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/x-ai/grok-2-vision-1212">xAI: Grok 2 Vision 1212</a><code class="text-xs">x-ai/grok-2-vision-1212</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="xAI: Grok 2 1212">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/x-ai/grok-2-1212">xAI: Grok 2 1212</a><code class="text-xs">x-ai/grok-2-1212</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command R7B (12-2024)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command-r7b-12-2024">Cohere: Command R7B (12-2024)</a><code class="text-xs">cohere/command-r7b-12-2024</code></td>
<td class="text-center">
<div>$0,038</div>
</td>
<td class="text-center">
<div>$0,15</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 2.0 Flash Experimental (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-2.0-flash-exp:free">Google: Gemini 2.0 Flash Experimental (free)</a><code class="text-xs">google/gemini-2.0-flash-exp:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">1 048 576</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.3 70B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.3-70b-instruct:free">Meta: Llama 3.3 70B Instruct (free)</a><code class="text-xs">meta-llama/llama-3.3-70b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">65 536</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.3 70B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.3-70b-instruct">Meta: Llama 3.3 70B Instruct</a><code class="text-xs">meta-llama/llama-3.3-70b-instruct</code></td>
<td class="text-center">
<div>$0,038</div>
</td>
<td class="text-center">
<div>$0,12</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Amazon: Nova Lite 1.0">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/amazon/nova-lite-v1">Amazon: Nova Lite 1.0</a><code class="text-xs">amazon/nova-lite-v1</code></td>
<td class="text-center">
<div>$0,06</div>
</td>
<td class="text-center">
<div>$0,24</div>
</td>
<td><span class="flex justify-center gap-1">300 000</span></td>
</tr>
<tr class="text-sm" title="Amazon: Nova Micro 1.0">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/amazon/nova-micro-v1">Amazon: Nova Micro 1.0</a><code class="text-xs">amazon/nova-micro-v1</code></td>
<td class="text-center">
<div>$0,035</div>
</td>
<td class="text-center">
<div>$0,14</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Amazon: Nova Pro 1.0">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/amazon/nova-pro-v1">Amazon: Nova Pro 1.0</a><code class="text-xs">amazon/nova-pro-v1</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$3,20</div>
</td>
<td><span class="flex justify-center gap-1">300 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: QwQ 32B Preview">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwq-32b-preview">Qwen: QwQ 32B Preview</a><code class="text-xs">qwen/qwq-32b-preview</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o (2024-11-20)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o-2024-11-20">OpenAI: GPT-4o (2024-11-20)</a><code class="text-xs">openai/gpt-4o-2024-11-20</code></td>
<td class="text-center">
<div>$2,50</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Mistral Large 2411">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-large-2411">Mistral Large 2411</a><code class="text-xs">mistralai/mistral-large-2411</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$6</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Mistral Large 2407">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-large-2407">Mistral Large 2407</a><code class="text-xs">mistralai/mistral-large-2407</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$6</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Mistral: Pixtral Large 2411">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/pixtral-large-2411">Mistral: Pixtral Large 2411</a><code class="text-xs">mistralai/pixtral-large-2411</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$6</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="xAI: Grok Vision Beta">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/x-ai/grok-vision-beta">xAI: Grok Vision Beta</a><code class="text-xs">x-ai/grok-vision-beta</code></td>
<td class="text-center">
<div>$5</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Infermatic: Mistral Nemo Inferor 12B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/infermatic/mn-inferor-12b">Infermatic: Mistral Nemo Inferor 12B</a><code class="text-xs">infermatic/mn-inferor-12b</code></td>
<td class="text-center">
<div>$0,60</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Qwen2.5 Coder 32B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-2.5-coder-32b-instruct:free">Qwen2.5 Coder 32B Instruct (free)</a><code class="text-xs">qwen/qwen-2.5-coder-32b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Qwen2.5 Coder 32B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-2.5-coder-32b-instruct">Qwen2.5 Coder 32B Instruct</a><code class="text-xs">qwen/qwen-2.5-coder-32b-instruct</code></td>
<td class="text-center">
<div>$0,05</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="SorcererLM 8x22B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/raifle/sorcererlm-8x22b">SorcererLM 8x22B</a><code class="text-xs">raifle/sorcererlm-8x22b</code></td>
<td class="text-center">
<div>$4,50</div>
</td>
<td class="text-center">
<div>$4,50</div>
</td>
<td><span class="flex justify-center gap-1">16 000</span></td>
</tr>
<tr class="text-sm" title="TheDrummer: UnslopNemo 12B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thedrummer/unslopnemo-12b">TheDrummer: UnslopNemo 12B</a><code class="text-xs">thedrummer/unslopnemo-12b</code></td>
<td class="text-center">
<div>$0,40</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.5 Haiku (2024-10-22)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.5-haiku-20241022">Anthropic: Claude 3.5 Haiku (2024-10-22)</a><code class="text-xs">anthropic/claude-3.5-haiku-20241022</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$4</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.5 Haiku (self-moderated)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.5-haiku:beta">Anthropic: Claude 3.5 Haiku (self-moderated)</a><code class="text-xs">anthropic/claude-3.5-haiku:beta</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$4</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.5 Haiku">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.5-haiku">Anthropic: Claude 3.5 Haiku</a><code class="text-xs">anthropic/claude-3.5-haiku</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$4</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.5 Sonnet (self-moderated)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.5-sonnet:beta">Anthropic: Claude 3.5 Sonnet (self-moderated)</a><code class="text-xs">anthropic/claude-3.5-sonnet:beta</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.5 Sonnet">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.5-sonnet">Anthropic: Claude 3.5 Sonnet</a><code class="text-xs">anthropic/claude-3.5-sonnet</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Magnum v4 72B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthracite-org/magnum-v4-72b">Magnum v4 72B</a><code class="text-xs">anthracite-org/magnum-v4-72b</code></td>
<td class="text-center">
<div>$2,50</div>
</td>
<td class="text-center">
<div>$3</div>
</td>
<td><span class="flex justify-center gap-1">16 384</span></td>
</tr>
<tr class="text-sm" title="Mistral: Ministral 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/ministral-8b">Mistral: Ministral 8B</a><code class="text-xs">mistralai/ministral-8b</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Ministral 3B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/ministral-3b">Mistral: Ministral 3B</a><code class="text-xs">mistralai/ministral-3b</code></td>
<td class="text-center">
<div>$0,04</div>
</td>
<td class="text-center">
<div>$0,04</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Qwen2.5 7B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-2.5-7b-instruct">Qwen2.5 7B Instruct</a><code class="text-xs">qwen/qwen-2.5-7b-instruct</code></td>
<td class="text-center">
<div>$0,04</div>
</td>
<td class="text-center">
<div>$0,10</div>
</td>
<td><span class="flex justify-center gap-1">65 536</span></td>
</tr>
<tr class="text-sm" title="NVIDIA: Llama 3.1 Nemotron 70B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nvidia/llama-3.1-nemotron-70b-instruct">NVIDIA: Llama 3.1 Nemotron 70B Instruct</a><code class="text-xs">nvidia/llama-3.1-nemotron-70b-instruct</code></td>
<td class="text-center">
<div>$0,12</div>
</td>
<td class="text-center">
<div>$0,30</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Inflection: Inflection 3 Productivity">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/inflection/inflection-3-productivity">Inflection: Inflection 3 Productivity</a><code class="text-xs">inflection/inflection-3-productivity</code></td>
<td class="text-center">
<div>$2,50</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">8 000</span></td>
</tr>
<tr class="text-sm" title="Inflection: Inflection 3 Pi">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/inflection/inflection-3-pi">Inflection: Inflection 3 Pi</a><code class="text-xs">inflection/inflection-3-pi</code></td>
<td class="text-center">
<div>$2,50</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">8 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 1.5 Flash 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-flash-1.5-8b">Google: Gemini 1.5 Flash 8B</a><code class="text-xs">google/gemini-flash-1.5-8b</code></td>
<td class="text-center">
<div>$0,038</div>
</td>
<td class="text-center">
<div>$0,15</div>
</td>
<td><span class="flex justify-center gap-1">1 000 000</span></td>
</tr>
<tr class="text-sm" title="Magnum v2 72B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthracite-org/magnum-v2-72b">Magnum v2 72B</a><code class="text-xs">anthracite-org/magnum-v2-72b</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$3</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="TheDrummer: Rocinante 12B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/thedrummer/rocinante-12b">TheDrummer: Rocinante 12B</a><code class="text-xs">thedrummer/rocinante-12b</code></td>
<td class="text-center">
<div>$0,19</div>
</td>
<td class="text-center">
<div>$0,45</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Liquid: LFM 40B MoE">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/liquid/lfm-40b">Liquid: LFM 40B MoE</a><code class="text-xs">liquid/lfm-40b</code></td>
<td class="text-center">
<div>$0,15</div>
</td>
<td class="text-center">
<div>$0,15</div>
</td>
<td><span class="flex justify-center gap-1">65 536</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.2 3B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.2-3b-instruct:free">Meta: Llama 3.2 3B Instruct (free)</a><code class="text-xs">meta-llama/llama-3.2-3b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.2 3B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.2-3b-instruct">Meta: Llama 3.2 3B Instruct</a><code class="text-xs">meta-llama/llama-3.2-3b-instruct</code></td>
<td class="text-center">
<div>$0,003</div>
</td>
<td class="text-center">
<div>$0,006</div>
</td>
<td><span class="flex justify-center gap-1">20 000</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.2 11B Vision Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.2-11b-vision-instruct:free">Meta: Llama 3.2 11B Vision Instruct (free)</a><code class="text-xs">meta-llama/llama-3.2-11b-vision-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.2 11B Vision Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.2-11b-vision-instruct">Meta: Llama 3.2 11B Vision Instruct</a><code class="text-xs">meta-llama/llama-3.2-11b-vision-instruct</code></td>
<td class="text-center">
<div>$0,049</div>
</td>
<td class="text-center">
<div>$0,049</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.2 90B Vision Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.2-90b-vision-instruct">Meta: Llama 3.2 90B Vision Instruct</a><code class="text-xs">meta-llama/llama-3.2-90b-vision-instruct</code></td>
<td class="text-center">
<div>$1,20</div>
</td>
<td class="text-center">
<div>$1,20</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.2 1B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.2-1b-instruct">Meta: Llama 3.2 1B Instruct</a><code class="text-xs">meta-llama/llama-3.2-1b-instruct</code></td>
<td class="text-center">
<div>$0,005</div>
</td>
<td class="text-center">
<div>$0,01</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Qwen2.5 72B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-2.5-72b-instruct:free">Qwen2.5 72B Instruct (free)</a><code class="text-xs">qwen/qwen-2.5-72b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Qwen2.5 72B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-2.5-72b-instruct">Qwen2.5 72B Instruct</a><code class="text-xs">qwen/qwen-2.5-72b-instruct</code></td>
<td class="text-center">
<div>$0,067</div>
</td>
<td class="text-center">
<div>$0,267</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="NeverSleep: Lumimaid v0.2 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/neversleep/llama-3.1-lumimaid-8b">NeverSleep: Lumimaid v0.2 8B</a><code class="text-xs">neversleep/llama-3.1-lumimaid-8b</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,90</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o1-mini">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o1-mini">OpenAI: o1-mini</a><code class="text-xs">openai/o1-mini</code></td>
<td class="text-center">
<div>$1,10</div>
</td>
<td class="text-center">
<div>$4,40</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: o1-mini (2024-09-12)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/o1-mini-2024-09-12">OpenAI: o1-mini (2024-09-12)</a><code class="text-xs">openai/o1-mini-2024-09-12</code></td>
<td class="text-center">
<div>$1,10</div>
</td>
<td class="text-center">
<div>$4,40</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Mistral: Pixtral 12B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/pixtral-12b">Mistral: Pixtral 12B</a><code class="text-xs">mistralai/pixtral-12b</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,10</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command R+ (08-2024)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command-r-plus-08-2024">Cohere: Command R+ (08-2024)</a><code class="text-xs">cohere/command-r-plus-08-2024</code></td>
<td class="text-center">
<div>$2,50</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command R (08-2024)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command-r-08-2024">Cohere: Command R (08-2024)</a><code class="text-xs">cohere/command-r-08-2024</code></td>
<td class="text-center">
<div>$0,15</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Qwen: Qwen2.5-VL 7B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-2.5-vl-7b-instruct">Qwen: Qwen2.5-VL 7B Instruct</a><code class="text-xs">qwen/qwen-2.5-vl-7b-instruct</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Sao10K: Llama 3.1 Euryale 70B v2.2">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/sao10k/l3.1-euryale-70b">Sao10K: Llama 3.1 Euryale 70B v2.2</a><code class="text-xs">sao10k/l3.1-euryale-70b</code></td>
<td class="text-center">
<div>$0,65</div>
</td>
<td class="text-center">
<div>$0,75</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Microsoft: Phi-3.5 Mini 128K Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/phi-3.5-mini-128k-instruct">Microsoft: Phi-3.5 Mini 128K Instruct</a><code class="text-xs">microsoft/phi-3.5-mini-128k-instruct</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Nous: Hermes 3 70B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nousresearch/hermes-3-llama-3.1-70b">Nous: Hermes 3 70B Instruct</a><code class="text-xs">nousresearch/hermes-3-llama-3.1-70b</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,28</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Nous: Hermes 3 405B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nousresearch/hermes-3-llama-3.1-405b">Nous: Hermes 3 405B Instruct</a><code class="text-xs">nousresearch/hermes-3-llama-3.1-405b</code></td>
<td class="text-center">
<div>$0,70</div>
</td>
<td class="text-center">
<div>$0,80</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="OpenAI: ChatGPT-4o">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/chatgpt-4o-latest">OpenAI: ChatGPT-4o</a><code class="text-xs">openai/chatgpt-4o-latest</code></td>
<td class="text-center">
<div>$5</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Sao10K: Llama 3 8B Lunaris">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/sao10k/l3-lunaris-8b">Sao10K: Llama 3 8B Lunaris</a><code class="text-xs">sao10k/l3-lunaris-8b</code></td>
<td class="text-center">
<div>$0,02</div>
</td>
<td class="text-center">
<div>$0,05</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o (2024-08-06)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o-2024-08-06">OpenAI: GPT-4o (2024-08-06)</a><code class="text-xs">openai/gpt-4o-2024-08-06</code></td>
<td class="text-center">
<div>$2,50</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.1 405B (base)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.1-405b">Meta: Llama 3.1 405B (base)</a><code class="text-xs">meta-llama/llama-3.1-405b</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$2</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.1 405B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.1-405b-instruct:free">Meta: Llama 3.1 405B Instruct (free)</a><code class="text-xs">meta-llama/llama-3.1-405b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">65 536</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.1 405B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.1-405b-instruct">Meta: Llama 3.1 405B Instruct</a><code class="text-xs">meta-llama/llama-3.1-405b-instruct</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$0,80</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.1 8B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.1-8b-instruct">Meta: Llama 3.1 8B Instruct</a><code class="text-xs">meta-llama/llama-3.1-8b-instruct</code></td>
<td class="text-center">
<div>$0,015</div>
</td>
<td class="text-center">
<div>$0,02</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3.1 70B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3.1-70b-instruct">Meta: Llama 3.1 70B Instruct</a><code class="text-xs">meta-llama/llama-3.1-70b-instruct</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,28</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Nemo (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-nemo:free">Mistral: Mistral Nemo (free)</a><code class="text-xs">mistralai/mistral-nemo:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral Nemo">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-nemo">Mistral: Mistral Nemo</a><code class="text-xs">mistralai/mistral-nemo</code></td>
<td class="text-center">
<div>$0,008</div>
</td>
<td class="text-center">
<div>$0,05</div>
</td>
<td><span class="flex justify-center gap-1">32 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o-mini">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o-mini">OpenAI: GPT-4o-mini</a><code class="text-xs">openai/gpt-4o-mini</code></td>
<td class="text-center">
<div>$0,15</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o-mini (2024-07-18)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o-mini-2024-07-18">OpenAI: GPT-4o-mini (2024-07-18)</a><code class="text-xs">openai/gpt-4o-mini-2024-07-18</code></td>
<td class="text-center">
<div>$0,15</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 2 27B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-2-27b-it">Google: Gemma 2 27B</a><code class="text-xs">google/gemma-2-27b-it</code></td>
<td class="text-center">
<div>$0,65</div>
</td>
<td class="text-center">
<div>$0,65</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 2 9B (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-2-9b-it:free">Google: Gemma 2 9B (free)</a><code class="text-xs">google/gemma-2-9b-it:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Google: Gemma 2 9B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemma-2-9b-it">Google: Gemma 2 9B</a><code class="text-xs">google/gemma-2-9b-it</code></td>
<td class="text-center">
<div>$0,01</div>
</td>
<td class="text-center">
<div>$0,01</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.5 Sonnet (2024-06-20) (self-moderated)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.5-sonnet-20240620:beta">Anthropic: Claude 3.5 Sonnet (2024-06-20) (self-moderated)</a><code class="text-xs">anthropic/claude-3.5-sonnet-20240620:beta</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3.5 Sonnet (2024-06-20)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3.5-sonnet-20240620">Anthropic: Claude 3.5 Sonnet (2024-06-20)</a><code class="text-xs">anthropic/claude-3.5-sonnet-20240620</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Sao10k: Llama 3 Euryale 70B v2.1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/sao10k/l3-euryale-70b">Sao10k: Llama 3 Euryale 70B v2.1</a><code class="text-xs">sao10k/l3-euryale-70b</code></td>
<td class="text-center">
<div>$1,48</div>
</td>
<td class="text-center">
<div>$1,48</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Dolphin 2.9.2 Mixtral 8x22B 🐬">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cognitivecomputations/dolphin-mixtral-8x22b">Dolphin 2.9.2 Mixtral 8x22B 🐬</a><code class="text-xs">cognitivecomputations/dolphin-mixtral-8x22b</code></td>
<td class="text-center">
<div>$0,90</div>
</td>
<td class="text-center">
<div>$0,90</div>
</td>
<td><span class="flex justify-center gap-1">16 000</span></td>
</tr>
<tr class="text-sm" title="Qwen 2 72B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/qwen/qwen-2-72b-instruct">Qwen 2 72B Instruct</a><code class="text-xs">qwen/qwen-2-72b-instruct</code></td>
<td class="text-center">
<div>$0,90</div>
</td>
<td class="text-center">
<div>$0,90</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral 7B Instruct v0.3">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-7b-instruct-v0.3">Mistral: Mistral 7B Instruct v0.3</a><code class="text-xs">mistralai/mistral-7b-instruct-v0.3</code></td>
<td class="text-center">
<div>$0,028</div>
</td>
<td class="text-center">
<div>$0,054</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral 7B Instruct (free)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-7b-instruct:free">Mistral: Mistral 7B Instruct (free)</a><code class="text-xs">mistralai/mistral-7b-instruct:free</code></td>
<td class="text-center">
<div>$0</div>
</td>
<td class="text-center">
<div>$0</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral 7B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-7b-instruct">Mistral: Mistral 7B Instruct</a><code class="text-xs">mistralai/mistral-7b-instruct</code></td>
<td class="text-center">
<div>$0,028</div>
</td>
<td class="text-center">
<div>$0,054</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="NousResearch: Hermes 2 Pro - Llama-3 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nousresearch/hermes-2-pro-llama-3-8b">NousResearch: Hermes 2 Pro - Llama-3 8B</a><code class="text-xs">nousresearch/hermes-2-pro-llama-3-8b</code></td>
<td class="text-center">
<div>$0,025</div>
</td>
<td class="text-center">
<div>$0,04</div>
</td>
<td><span class="flex justify-center gap-1">131 072</span></td>
</tr>
<tr class="text-sm" title="Microsoft: Phi-3 Mini 128K Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/phi-3-mini-128k-instruct">Microsoft: Phi-3 Mini 128K Instruct</a><code class="text-xs">microsoft/phi-3-mini-128k-instruct</code></td>
<td class="text-center">
<div>$0,10</div>
</td>
<td class="text-center">
<div>$0,10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Microsoft: Phi-3 Medium 128K Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/phi-3-medium-128k-instruct">Microsoft: Phi-3 Medium 128K Instruct</a><code class="text-xs">microsoft/phi-3-medium-128k-instruct</code></td>
<td class="text-center">
<div>$1</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="NeverSleep: Llama 3 Lumimaid 70B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/neversleep/llama-3-lumimaid-70b">NeverSleep: Llama 3 Lumimaid 70B</a><code class="text-xs">neversleep/llama-3-lumimaid-70b</code></td>
<td class="text-center">
<div>$4</div>
</td>
<td class="text-center">
<div>$6</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 1.5 Flash ">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-flash-1.5">Google: Gemini 1.5 Flash</a><code class="text-xs">google/gemini-flash-1.5</code></td>
<td class="text-center">
<div>$0,075</div>
</td>
<td class="text-center">
<div>$0,30</div>
</td>
<td><span class="flex justify-center gap-1">1 000 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o (2024-05-13)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o-2024-05-13">OpenAI: GPT-4o (2024-05-13)</a><code class="text-xs">openai/gpt-4o-2024-05-13</code></td>
<td class="text-center">
<div>$5</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o">OpenAI: GPT-4o</a><code class="text-xs">openai/gpt-4o</code></td>
<td class="text-center">
<div>$2,50</div>
</td>
<td class="text-center">
<div>$10</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4o (extended)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4o:extended">OpenAI: GPT-4o (extended)</a><code class="text-xs">openai/gpt-4o:extended</code></td>
<td class="text-center">
<div>$6</div>
</td>
<td class="text-center">
<div>$18</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Meta: LlamaGuard 2 8B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-guard-2-8b">Meta: LlamaGuard 2 8B</a><code class="text-xs">meta-llama/llama-guard-2-8b</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Fimbulvetr 11B v2">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/sao10k/fimbulvetr-11b-v2">Fimbulvetr 11B v2</a><code class="text-xs">sao10k/fimbulvetr-11b-v2</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$1,20</div>
</td>
<td><span class="flex justify-center gap-1">4 096</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3 70B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3-70b-instruct">Meta: Llama 3 70B Instruct</a><code class="text-xs">meta-llama/llama-3-70b-instruct</code></td>
<td class="text-center">
<div>$0,30</div>
</td>
<td class="text-center">
<div>$0,40</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Meta: Llama 3 8B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/meta-llama/llama-3-8b-instruct">Meta: Llama 3 8B Instruct</a><code class="text-xs">meta-llama/llama-3-8b-instruct</code></td>
<td class="text-center">
<div>$0,03</div>
</td>
<td class="text-center">
<div>$0,06</div>
</td>
<td><span class="flex justify-center gap-1">8 192</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mixtral 8x22B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mixtral-8x22b-instruct">Mistral: Mixtral 8x22B Instruct</a><code class="text-xs">mistralai/mixtral-8x22b-instruct</code></td>
<td class="text-center">
<div>$0,90</div>
</td>
<td class="text-center">
<div>$0,90</div>
</td>
<td><span class="flex justify-center gap-1">65 536</span></td>
</tr>
<tr class="text-sm" title="WizardLM-2 8x22B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/microsoft/wizardlm-2-8x22b">WizardLM-2 8x22B</a><code class="text-xs">microsoft/wizardlm-2-8x22b</code></td>
<td class="text-center">
<div>$0,48</div>
</td>
<td class="text-center">
<div>$0,48</div>
</td>
<td><span class="flex justify-center gap-1">65 536</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4 Turbo">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4-turbo">OpenAI: GPT-4 Turbo</a><code class="text-xs">openai/gpt-4-turbo</code></td>
<td class="text-center">
<div>$10</div>
</td>
<td class="text-center">
<div>$30</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Google: Gemini 1.5 Pro">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/google/gemini-pro-1.5">Google: Gemini 1.5 Pro</a><code class="text-xs">google/gemini-pro-1.5</code></td>
<td class="text-center">
<div>$1,25</div>
</td>
<td class="text-center">
<div>$5</div>
</td>
<td><span class="flex justify-center gap-1">2 000 000</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command R+">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command-r-plus">Cohere: Command R+</a><code class="text-xs">cohere/command-r-plus</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command R+ (04-2024)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command-r-plus-04-2024">Cohere: Command R+ (04-2024)</a><code class="text-xs">cohere/command-r-plus-04-2024</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$15</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Midnight Rose 70B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/sophosympatheia/midnight-rose-70b">Midnight Rose 70B</a><code class="text-xs">sophosympatheia/midnight-rose-70b</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$0,80</div>
</td>
<td><span class="flex justify-center gap-1">4 096</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command R">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command-r">Cohere: Command R</a><code class="text-xs">cohere/command-r</code></td>
<td class="text-center">
<div>$0,50</div>
</td>
<td class="text-center">
<div>$1,50</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command">Cohere: Command</a><code class="text-xs">cohere/command</code></td>
<td class="text-center">
<div>$1</div>
</td>
<td class="text-center">
<div>$2</div>
</td>
<td><span class="flex justify-center gap-1">4 096</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3 Haiku (self-moderated)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3-haiku:beta">Anthropic: Claude 3 Haiku (self-moderated)</a><code class="text-xs">anthropic/claude-3-haiku:beta</code></td>
<td class="text-center">
<div>$0,25</div>
</td>
<td class="text-center">
<div>$1,25</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3 Haiku">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3-haiku">Anthropic: Claude 3 Haiku</a><code class="text-xs">anthropic/claude-3-haiku</code></td>
<td class="text-center">
<div>$0,25</div>
</td>
<td class="text-center">
<div>$1,25</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3 Opus (self-moderated)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3-opus:beta">Anthropic: Claude 3 Opus (self-moderated)</a><code class="text-xs">anthropic/claude-3-opus:beta</code></td>
<td class="text-center">
<div>$15</div>
</td>
<td class="text-center">
<div>$75</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Anthropic: Claude 3 Opus">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/anthropic/claude-3-opus">Anthropic: Claude 3 Opus</a><code class="text-xs">anthropic/claude-3-opus</code></td>
<td class="text-center">
<div>$15</div>
</td>
<td class="text-center">
<div>$75</div>
</td>
<td><span class="flex justify-center gap-1">200 000</span></td>
</tr>
<tr class="text-sm" title="Cohere: Command R (03-2024)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/cohere/command-r-03-2024">Cohere: Command R (03-2024)</a><code class="text-xs">cohere/command-r-03-2024</code></td>
<td class="text-center">
<div>$0,50</div>
</td>
<td class="text-center">
<div>$1,50</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Mistral Large">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-large">Mistral Large</a><code class="text-xs">mistralai/mistral-large</code></td>
<td class="text-center">
<div>$2</div>
</td>
<td class="text-center">
<div>$6</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-3.5 Turbo (older v0613)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-3.5-turbo-0613">OpenAI: GPT-3.5 Turbo (older v0613)</a><code class="text-xs">openai/gpt-3.5-turbo-0613</code></td>
<td class="text-center">
<div>$1</div>
</td>
<td class="text-center">
<div>$2</div>
</td>
<td><span class="flex justify-center gap-1">4 095</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4 Turbo Preview">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4-turbo-preview">OpenAI: GPT-4 Turbo Preview</a><code class="text-xs">openai/gpt-4-turbo-preview</code></td>
<td class="text-center">
<div>$10</div>
</td>
<td class="text-center">
<div>$30</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="Nous: Hermes 2 Mixtral 8x7B DPO">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/nousresearch/nous-hermes-2-mixtral-8x7b-dpo">Nous: Hermes 2 Mixtral 8x7B DPO</a><code class="text-xs">nousresearch/nous-hermes-2-mixtral-8x7b-dpo</code></td>
<td class="text-center">
<div>$0,60</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral Small">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-small">Mistral Small</a><code class="text-xs">mistralai/mistral-small</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,60</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral Tiny">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-tiny">Mistral Tiny</a><code class="text-xs">mistralai/mistral-tiny</code></td>
<td class="text-center">
<div>$0,25</div>
</td>
<td class="text-center">
<div>$0,25</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral 7B Instruct v0.2">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-7b-instruct-v0.2">Mistral: Mistral 7B Instruct v0.2</a><code class="text-xs">mistralai/mistral-7b-instruct-v0.2</code></td>
<td class="text-center">
<div>$0,20</div>
</td>
<td class="text-center">
<div>$0,20</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mixtral 8x7B Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mixtral-8x7b-instruct">Mistral: Mixtral 8x7B Instruct</a><code class="text-xs">mistralai/mixtral-8x7b-instruct</code></td>
<td class="text-center">
<div>$0,08</div>
</td>
<td class="text-center">
<div>$0,24</div>
</td>
<td><span class="flex justify-center gap-1">32 768</span></td>
</tr>
<tr class="text-sm" title="Noromaid 20B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/neversleep/noromaid-20b">Noromaid 20B</a><code class="text-xs">neversleep/noromaid-20b</code></td>
<td class="text-center">
<div>$1</div>
</td>
<td class="text-center">
<div>$1,75</div>
</td>
<td><span class="flex justify-center gap-1">4 096</span></td>
</tr>
<tr class="text-sm" title="Toppy M 7B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/undi95/toppy-m-7b">Toppy M 7B</a><code class="text-xs">undi95/toppy-m-7b</code></td>
<td class="text-center">
<div>$0,80</div>
</td>
<td class="text-center">
<div>$1,20</div>
</td>
<td><span class="flex justify-center gap-1">4 096</span></td>
</tr>
<tr class="text-sm" title="Goliath 120B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/alpindale/goliath-120b">Goliath 120B</a><code class="text-xs">alpindale/goliath-120b</code></td>
<td class="text-center">
<div>$9</div>
</td>
<td class="text-center">
<div>$11</div>
</td>
<td><span class="flex justify-center gap-1">6 144</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4 Turbo (older v1106)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4-1106-preview">OpenAI: GPT-4 Turbo (older v1106)</a><code class="text-xs">openai/gpt-4-1106-preview</code></td>
<td class="text-center">
<div>$10</div>
</td>
<td class="text-center">
<div>$30</div>
</td>
<td><span class="flex justify-center gap-1">128 000</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-3.5 Turbo Instruct">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-3.5-turbo-instruct">OpenAI: GPT-3.5 Turbo Instruct</a><code class="text-xs">openai/gpt-3.5-turbo-instruct</code></td>
<td class="text-center">
<div>$1,50</div>
</td>
<td class="text-center">
<div>$2</div>
</td>
<td><span class="flex justify-center gap-1">4 095</span></td>
</tr>
<tr class="text-sm" title="Mistral: Mistral 7B Instruct v0.1">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mistralai/mistral-7b-instruct-v0.1">Mistral: Mistral 7B Instruct v0.1</a><code class="text-xs">mistralai/mistral-7b-instruct-v0.1</code></td>
<td class="text-center">
<div>$0,11</div>
</td>
<td class="text-center">
<div>$0,19</div>
</td>
<td><span class="flex justify-center gap-1">2 824</span></td>
</tr>
<tr class="text-sm" title="Pygmalion: Mythalion 13B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/pygmalionai/mythalion-13b">Pygmalion: Mythalion 13B</a><code class="text-xs">pygmalionai/mythalion-13b</code></td>
<td class="text-center">
<div>$0,60</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">4 096</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-3.5 Turbo 16k">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-3.5-turbo-16k">OpenAI: GPT-3.5 Turbo 16k</a><code class="text-xs">openai/gpt-3.5-turbo-16k</code></td>
<td class="text-center">
<div>$3</div>
</td>
<td class="text-center">
<div>$4</div>
</td>
<td><span class="flex justify-center gap-1">16 385</span></td>
</tr>
<tr class="text-sm" title="Mancer: Weaver (alpha)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/mancer/weaver">Mancer: Weaver (alpha)</a><code class="text-xs">mancer/weaver</code></td>
<td class="text-center">
<div>$1,50</div>
</td>
<td class="text-center">
<div>$1,50</div>
</td>
<td><span class="flex justify-center gap-1">8 000</span></td>
</tr>
<tr class="text-sm" title="ReMM SLERP 13B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/undi95/remm-slerp-l2-13b">ReMM SLERP 13B</a><code class="text-xs">undi95/remm-slerp-l2-13b</code></td>
<td class="text-center">
<div>$0,70</div>
</td>
<td class="text-center">
<div>$1</div>
</td>
<td><span class="flex justify-center gap-1">6 144</span></td>
</tr>
<tr class="text-sm" title="MythoMax 13B">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/gryphe/mythomax-l2-13b">MythoMax 13B</a><code class="text-xs">gryphe/mythomax-l2-13b</code></td>
<td class="text-center">
<div>$0,06</div>
</td>
<td class="text-center">
<div>$0,06</div>
</td>
<td><span class="flex justify-center gap-1">4 096</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-3.5 Turbo">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-3.5-turbo">OpenAI: GPT-3.5 Turbo</a><code class="text-xs">openai/gpt-3.5-turbo</code></td>
<td class="text-center">
<div>$0,50</div>
</td>
<td class="text-center">
<div>$1,50</div>
</td>
<td><span class="flex justify-center gap-1">16 385</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4 (older v0314)">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4-0314">OpenAI: GPT-4 (older v0314)</a><code class="text-xs">openai/gpt-4-0314</code></td>
<td class="text-center">
<div>$30</div>
</td>
<td class="text-center">
<div>$60</div>
</td>
<td><span class="flex justify-center gap-1">8 191</span></td>
</tr>
<tr class="text-sm" title="OpenAI: GPT-4">
<td class="break-words py-2"><a class="block text-slate-11 underline" href="https://openrouter.ai/openai/gpt-4">OpenAI: GPT-4</a><code class="text-xs">openai/gpt-4</code></td>
<td class="text-center">
<div>$30</div>
</td>
<td class="text-center">
<div>$60</div>
</td>
<td><span class="flex justify-center gap-1">8 191&nbsp;</span></td>
</tr>
</tbody>
</table></div></div></body></html>