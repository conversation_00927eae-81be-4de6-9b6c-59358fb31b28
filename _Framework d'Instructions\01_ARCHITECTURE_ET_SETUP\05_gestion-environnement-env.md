Gestion des Variables d'Environnement (.env)

**Objectif :** Ce document définit les procédures pour gérer les variables d'environnement de manière sécurisée et efficace, notamment pour les clés API et les configurations sensibles.

**Directives pour l'agent de codage :**

*   **Sécurité des informations :** Soyez extrêmement vigilant quant à la manipulation des variables d'environnement. Les clés sensibles ne doivent **jamais** être commises directement dans le dépôt de code.
*   **Bonnes pratiques :** Référez-vous aux meilleures pratiques de gestion des `.env` dans les applications React/Next.js. Context7 pourrait potentiellement fournir des informations sur les configurations standardisées pour différents frameworks (ex: `NEXT_PUBLIC_` pour Next.js).
*   **Augment Code pour la détection :** Augment Code peut vous aider à identifier les variables d'environnement dans le code et suggérer des améliorations pour leur gestion ou leur obfuscation si nécessaire, car il comprend la codebase.

Ce fichier abordera la création du fichier `.env.local`, l'intégration des variables dans le code de l'application et les meilleures pratiques de sécurité.

---

## **CONFIGURATION GEMINI API - MÉTHODE COMPLÈTE FLEXODIV**

### **Informations API**

**Clé API Actuelle :**
```
AIzaSyCqooMDW5sKHciKAX19JUolQT01RDhBysc
```

**Modèle Utilisé :**
- **Modèle :** `gemini-2.5-flash`
- **Provider :** Google AI Studio
- **Type :** Chat conversationnel

### **Configuration Locale**

**1. Fichier .env.local**
```bash
# Configuration Assistant IA FlexoDiv
VITE_GEMINI_API_KEY=AIzaSyCqooMDW5sKHciKAX19JUolQT01RDhBysc
```

**2. Configuration Vite (vite.config.ts)**
```typescript
export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.VITE_GEMINI_API_KEY': JSON.stringify(env.VITE_GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.VITE_GEMINI_API_KEY)
      }
    };
});
```

**3. Service Gemini (services/geminiService.ts)**
```typescript
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

if (!API_KEY) {
    console.warn("VITE_GEMINI_API_KEY environment variable not set.");
}

const ai = new GoogleGenAI({ apiKey: API_KEY || "fallback_key" });
```

### **Configuration Netlify**

**Variables d'Environnement :**
1. **Dashboard Netlify** → Site settings → Environment variables
2. **Ajouter variable :**
   - **Nom :** `VITE_GEMINI_API_KEY`
   - **Valeur :** `AIzaSyCqooMDW5sKHciKAX19JUolQT01RDhBysc`
   - **Scopes :** Production + Deploy previews

**Redéploiement Obligatoire :**
Après ajout/modification variable → Trigger deploy

### **Fonctionnalités Assistant**

**Capacités :**
- Réponses en français uniquement
- Base de connaissances FlexoDiv intégrée
- Streaming des réponses
- Gestion d'erreurs robuste
- Reset automatique tous les 10 messages

**Instructions Système :**
- Profil complet Francisco/FlexoDiv
- Compétences techniques détaillées
- Projets réalisés
- Services proposés
- Packages d'assistance avec tarifs

### **Tests & Vérification**

**Test Local :**
```bash
# Démarrer serveur dev
npm run dev

# Ouvrir http://localhost:5173
# Aller sur page Contact
# Tester assistant IA
```

**Test Production :**
```bash
# Vérifier sur https://flexodiv.netlify.app/
# Page Contact → Assistant IA
# Poser question test : "Qui est FlexoDiv ?"
```

**Vérifications Console :**
```javascript
// Dans DevTools Console
console.log('API Key:', import.meta.env.VITE_GEMINI_API_KEY);
// Doit afficher la clé (en dev) ou undefined (en prod)
```

### **Sécurité & Bonnes Pratiques**

**Sécurité :**
- ✅ Clé API côté client (limitation Google AI Studio)
- ✅ Pas de données sensibles dans les prompts
- ✅ Gestion d'erreurs pour éviter exposition
- ✅ Fallback si API indisponible

**Limitations :**
- Quota API Google (surveiller usage)
- Rate limiting (gestion automatique)
- Taille des réponses (optimisée)

**Monitoring :**
- Logs console pour erreurs API
- Fallback message si service indisponible
- Reset chat automatique pour éviter blocages

### **Maintenance**

**Renouvellement Clé :**
1. Google AI Studio → API Keys
2. Générer nouvelle clé
3. Mettre à jour .env.local
4. Mettre à jour Netlify variables
5. Redéployer

**Monitoring Usage :**
- Google AI Studio → Usage dashboard
- Surveiller quotas mensuels
- Optimiser si nécessaire

**Support :**
En cas de problème :
1. Vérifier clé API valide
2. Contrôler variables d'environnement
3. Tester en local d'abord
4. Vérifier logs Netlify
5. Consulter Google AI Studio status

---

## **CONFIGURATION ASSISTANT IA FLEXODIV - RÉSUMÉ COMPLET**

### **Configuration Terminée**

L'assistant IA FlexoDiv a été entièrement configuré avec les éléments suivants :

### **Sécurité de la clé API**
- ✅ Clé API sécurisée dans `.env.local`
- ✅ Variable d'environnement : `VITE_GEMINI_API_KEY`
- ✅ Fichier `.env.local` ignoré par Git (protection contre les commits accidentels)
- ✅ Fichier `.env.example` créé pour la documentation

### **Base de connaissances**
- ✅ Fichier `data/knowledge-base.ts` créé avec toutes les informations sur Francisco/FlexoDiv
- ✅ Profil personnel complet
- ✅ Compétences techniques détaillées
- ✅ Portfolio de projets avec descriptions
- ✅ Services et packages d'assistance avec tarifs
- ✅ Approche de développement et philosophie

### **Service Gemini**
- ✅ Service `services/geminiService.ts` mis à jour
- ✅ Intégration de la base de connaissances
- ✅ Instruction système enrichie et dynamique
- ✅ Gestion d'erreurs améliorée
- ✅ Support TypeScript complet

### **Documentation**
- ✅ `data/README-Assistant.md` - Documentation complète
- ✅ `data/test-assistant.js` - Script de test
- ✅ `vite-env.d.ts` - Types TypeScript pour Vite
- ✅ Ce fichier de résumé

### **Prêt à utiliser**

L'assistant est maintenant prêt à fonctionner avec :

1. **Clé API sécurisée** : `AIzaSyCqooMDW5sKHciKAX19JUolQT01RDhBysc`
2. **Base de connaissances complète** sur Francisco/FlexoDiv
3. **Personnalité professionnelle** et enthousiaste
4. **Réponses en français** uniquement
5. **Informations précises** sur compétences, projets et services

### **Fonctionnalités de l'assistant**

L'assistant peut maintenant répondre à des questions sur :
- Les compétences techniques de Francisco (React, GSAP, Three.js, IA, etc.)
- Les projets réalisés (Assistant Parcours, Developer Inspector, etc.)
- Les services proposés (Intégration IA, Frontend, Solutions créatives)
- Les packages d'assistance avec tarifs (9,99€ à 99,99€)
- L'approche de développement et la philosophie de travail
- Les technologies recommandées et les bonnes pratiques

### **Architecture Technique**

**Localisation :** `pages/Contact.tsx` (colonne droite)
**Composant principal :** `components/AIChat.tsx`
**Service :** `services/geminiService.ts`
**Base de données :** `data/knowledge-base.ts`

**Flux de données :**
1. Utilisateur tape message → `AIChat.tsx`
2. Appel service → `geminiService.ts`
3. Enrichissement avec base de connaissances
4. Streaming réponse Gemini
5. Affichage temps réel dans l'interface
