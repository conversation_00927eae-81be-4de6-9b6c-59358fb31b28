---
type: manual
description: "Intégration Firebase - Configuration, sécurité, Firestore et Cloud Functions"
---

# INTÉGRATION FIREBASE

## CONFIGURATION FIREBASE (SÉCURITÉ CRITIQUE)

### Firestore - Structure des Données
- **OBLIGATION** de définir et valider la structure avant tout codage
- Concevoir des requêtes efficaces et optimisées
- Implémenter l'indexation appropriée pour les performances
- Respecter les limites de Firestore (1 MB par document, 500 champs max)

### Variables d'Environnement Firebase
```env
# Configuration Firebase obligatoire dans .env.local
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef

# Variables serveur (sans préfixe VITE_)
FIREBASE_ADMIN_SDK_KEY=server_only_key
```

## RÈGLES DE SÉCURITÉ FIRESTORE

### Règles de Sécurité (firestore.rules)
- **ÉTAPE NON NÉGOCIABLE** : Rédiger des règles précises et restrictives
- Principe du moindre privilège : autoriser uniquement les accès nécessaires
- Tester les règles avec l'émulateur Firebase avant déploiement
- Documenter chaque règle avec des commentaires explicatifs

### Exemple de Règles Restrictives
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Accès utilisateur authentifié uniquement
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Données publiques en lecture seule
    match /public/{document} {
      allow read: if true;
      allow write: if request.auth != null && hasRole('admin');
    }
  }
}
```

## CLOUD FUNCTIONS

### Bonnes Pratiques Obligatoires
- **Fonctions petites**, spécifiques à une tâche unique
- **Implémentation idempotente** (même résultat si exécutée plusieurs fois)
- **Gestion d'erreurs robuste** avec retry automatique
- **Monitoring et logging** appropriés

### Pattern de Cloud Function
```typescript
import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';

export const processData = onCall(async (request) => {
  try {
    // Validation des données d'entrée
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'Utilisateur non authentifié');
    }

    const { data } = request;
    if (!data || !data.requiredField) {
      throw new HttpsError('invalid-argument', 'Données manquantes');
    }

    // Traitement de la logique métier
    const result = await performBusinessLogic(data);
    
    logger.info('Traitement réussi', { userId: request.auth.uid });
    return { success: true, result };

  } catch (error) {
    logger.error('Erreur lors du traitement', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'Erreur interne du serveur');
  }
});
```

## AUTHENTIFICATION FIREBASE

### Gestion des Tokens et Authentification
```typescript
// Stockage sécurisé des tokens
const storeToken = (token: string) => {
  // Utiliser httpOnly cookies ou sessionStorage selon le contexte
  sessionStorage.setItem('authToken', token);
};

// Validation des tokens
const validateToken = (token: string): boolean => {
  try {
    const decoded = jwt.decode(token);
    return decoded && decoded.exp > Date.now() / 1000;
  } catch {
    return false;
  }
};
```

## OPTIMISATIONS FIRESTORE

### Requêtes Efficaces
- Utiliser des index composites pour les requêtes complexes
- Limiter le nombre de documents récupérés avec `limit()`
- Utiliser la pagination avec `startAfter()` pour les grandes collections
- Éviter les requêtes `array-contains-any` sur de grandes collections

### Pattern de Requête Optimisée
```typescript
// Requête paginée efficace
const getDocumentsPaginated = async (lastDoc?: DocumentSnapshot) => {
  let query = collection(db, 'documents')
    .orderBy('createdAt', 'desc')
    .limit(10);

  if (lastDoc) {
    query = query.startAfter(lastDoc);
  }

  const snapshot = await getDocs(query);
  return {
    documents: snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })),
    lastDoc: snapshot.docs[snapshot.docs.length - 1]
  };
};
```

## SÉCURITÉ FIREBASE

### Protections Obligatoires
- Validation et sanitisation de tous les inputs utilisateur
- Protection contre les injections NoSQL
- Chiffrement des données sensibles côté client
- Audit des accès aux données sensibles

### Monitoring et Logs
- Logger tous les accès aux données sensibles
- Surveiller les tentatives d'accès non autorisées
- Implémenter des alertes pour les activités suspectes
- Maintenir des logs d'audit pour la conformité RGPD
