---
type: always
description: "Accessibilité et sécurité - Standards WCAG, sécurité frontend/backend, RGPD"
---

# ACCESSIBILITÉ ET SÉCURITÉ

## ACCESSIBILITÉ (A11Y) - CONFORMITÉ OBLIGATOIRE

### Standards WCAG 2.1 AA
**Exigences minimales :**
- **Contraste de couleurs conforme** (4.5:1 pour le texte normal)
- **Navigation au clavier complète** pour tous les éléments interactifs
- **Textes alternatifs** pour toutes les images
- **Structure sémantique HTML** appropriée

### Implémentation Technique Obligatoire
```typescript
// Boutons et interactions
<button
  aria-label="Fermer la modal"
  aria-expanded={isOpen}
  onClick={handleClose}
>
  ×
</button>

// Formulaires
<input
  aria-describedby="email-error"
  aria-invalid={hasError}
  type="email"
/>
{hasError && <div id="email-error" role="alert">Email invalide</div>}

// Navigation
<nav aria-label="Navigation principale">
  <ul role="list">
    <li><a href="/" aria-current="page">Accueil</a></li>
    <li><a href="/about">À propos</a></li>
  </ul>
</nav>
```

### Tests d'Accessibilité Obligatoires
**Outils obligatoires :**
- Tests automatisés avec `@axe-core/react`
- Validation manuelle avec lecteurs d'écran
- Tests de navigation au clavier
- Vérification des contrastes

## SÉCURITÉ FRONTEND

### Protections Obligatoires
- **Validation et sanitisation** de tous les inputs utilisateur
- **Protection contre XSS** (Cross-Site Scripting)
- **Utilisation de Content Security Policy** (CSP)
- **Chiffrement des données sensibles** côté client

### Pattern de Validation Sécurisée
```typescript
import DOMPurify from 'dompurify';

// Sanitisation des inputs utilisateur
const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, { 
    ALLOWED_TAGS: [], 
    ALLOWED_ATTR: [] 
  });
};

// Validation des données
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

// Composant de formulaire sécurisé
const SecureForm = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    
    const sanitizedEmail = sanitizeInput(email);
    
    if (!validateEmail(sanitizedEmail)) {
      setError('Format d\'email invalide');
      return;
    }
    
    // Traitement sécurisé...
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        aria-describedby={error ? "email-error" : undefined}
        aria-invalid={!!error}
      />
      {error && <div id="email-error" role="alert">{error}</div>}
    </form>
  );
};
```

### Content Security Policy (CSP)
```html
<!-- CSP obligatoire dans index.html -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: https:;
  font-src 'self';
  connect-src 'self' https://api.example.com;
">
```

## PROTECTION DES DONNÉES PERSONNELLES (RGPD)

### Conformité Obligatoire
- **Consentement explicite** pour les cookies
- **Droit à l'oubli** implémenté
- **Minimisation des données** collectées
- **Chiffrement des données sensibles**
- **Logs d'audit** pour les accès aux données

### Implémentation RGPD
```typescript
// Gestion des consentements
const CookieConsent = () => {
  const [consent, setConsent] = useState<boolean | null>(null);

  useEffect(() => {
    const savedConsent = localStorage.getItem('cookieConsent');
    if (savedConsent) {
      setConsent(JSON.parse(savedConsent));
    }
  }, []);

  const handleAccept = () => {
    setConsent(true);
    localStorage.setItem('cookieConsent', 'true');
    // Activer les cookies analytiques
  };

  const handleReject = () => {
    setConsent(false);
    localStorage.setItem('cookieConsent', 'false');
    // Désactiver les cookies non essentiels
  };

  if (consent !== null) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4">
      <p>Nous utilisons des cookies pour améliorer votre expérience.</p>
      <div className="flex gap-4 mt-2">
        <button onClick={handleAccept}>Accepter</button>
        <button onClick={handleReject}>Refuser</button>
      </div>
    </div>
  );
};
```

### Droit à l'Oubli
```typescript
// Fonction de suppression des données utilisateur
const deleteUserData = async (userId: string) => {
  try {
    // Supprimer les données de la base
    await deleteDoc(doc(db, 'users', userId));
    
    // Supprimer les fichiers associés
    await deleteObject(ref(storage, `users/${userId}`));
    
    // Logger l'action pour audit
    console.log(`Données supprimées pour l'utilisateur ${userId}`);
    
    return { success: true };
  } catch (error) {
    console.error('Erreur lors de la suppression:', error);
    throw new Error('Impossible de supprimer les données');
  }
};
```

## SÉCURITÉ BACKEND/API

### Authentification et Autorisation
```typescript
// Middleware d'authentification
const requireAuth = (req: Request, res: Response, next: NextFunction) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Token manquant' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Token invalide' });
  }
};

// Limitation du taux de requêtes
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limite à 100 requêtes par fenêtre
  message: 'Trop de requêtes, réessayez plus tard'
});
```

### Validation des Données Serveur
```typescript
import Joi from 'joi';

// Schéma de validation
const userSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  name: Joi.string().min(2).max(50).required()
});

// Middleware de validation
const validateUser = (req: Request, res: Response, next: NextFunction) => {
  const { error } = userSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({ 
      error: 'Données invalides', 
      details: error.details 
    });
  }
  
  next();
};
```

## MONITORING ET AUDIT DE SÉCURITÉ

### Logs de Sécurité Obligatoires
- **Tentatives de connexion** (réussies et échouées)
- **Accès aux données sensibles**
- **Modifications de données critiques**
- **Erreurs de sécurité** et tentatives d'intrusion

### Implémentation des Logs d'Audit
```typescript
// Service de logging sécurisé
const securityLogger = {
  logAuth: (userId: string, action: string, success: boolean) => {
    console.log(JSON.stringify({
      timestamp: new Date().toISOString(),
      type: 'AUTH',
      userId,
      action,
      success,
      ip: req.ip
    }));
  },
  
  logDataAccess: (userId: string, resource: string) => {
    console.log(JSON.stringify({
      timestamp: new Date().toISOString(),
      type: 'DATA_ACCESS',
      userId,
      resource
    }));
  }
};
```
