# **06_CONFIGURATION-OUTILS-DEV.MD - OUTILS DE DÉVELOPPEMENT**

## **OBJECTIF DE LA CONFIGURATION**

Ce document guide l'agent dans la configuration complète de l'environnement de développement, incluant tous les outils nécessaires pour maintenir la qualité du code et optimiser la productivité.

---

## **1. CONFIGURATION VITE.JS**

### **1.1 Configuration de Base**

**Fichier `vite.config.ts` obligatoire :**
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@types': path.resolve(__dirname, './src/types'),
      '@services': path.resolve(__dirname, './src/services'),
      '@assets': path.resolve(__dirname, './src/assets'),
    },
  },
  server: {
    port: 3000,
    open: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          firebase: ['firebase/app', 'firebase/auth', 'firebase/firestore'],
        },
      },
    },
  },
})
```

### **1.2 Plugins Vite Obligatoires**

**Installation requise :**
```bash
npm install -D @vitejs/plugin-react
npm install -D vite-plugin-eslint
npm install -D vite-bundle-analyzer
```

---

## **2. CONFIGURATION TYPESCRIPT**

### **2.1 Configuration tsconfig.json**

**Fichier `tsconfig.json` obligatoire :**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@components/*": ["./src/components/*"],
      "@pages/*": ["./src/pages/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@utils/*": ["./src/utils/*"],
      "@types/*": ["./src/types/*"],
      "@services/*": ["./src/services/*"],
      "@assets/*": ["./src/assets/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### **2.2 Types Globaux**

**Fichier `src/types/global.d.ts` :**
```typescript
// Types globaux pour l'application
declare global {
  interface Window {
    // Extensions window si nécessaire
  }
}

// Types pour les variables d'environnement
interface ImportMetaEnv {
  readonly VITE_FIREBASE_API_KEY: string
  readonly VITE_FIREBASE_AUTH_DOMAIN: string
  readonly VITE_FIREBASE_PROJECT_ID: string
  readonly VITE_FIREBASE_STORAGE_BUCKET: string
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string
  readonly VITE_FIREBASE_APP_ID: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

export {}
```

---

## **3. CONFIGURATION ESLINT**

### **3.1 Configuration ESLint**

**Fichier `.eslintrc.cjs` obligatoire :**
```javascript
module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
    'plugin:react/recommended',
    'plugin:react/jsx-runtime',
    'plugin:jsx-a11y/recommended',
  ],
  ignorePatterns: ['dist', '.eslintrc.cjs'],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh', 'jsx-a11y'],
  rules: {
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'error',
    'react/prop-types': 'off',
    'jsx-a11y/anchor-is-valid': 'error',
    'jsx-a11y/alt-text': 'error',
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
}
```

### **3.2 Installation des Dépendances ESLint**

**Commandes obligatoires :**
```bash
npm install -D eslint
npm install -D @typescript-eslint/eslint-plugin
npm install -D @typescript-eslint/parser
npm install -D eslint-plugin-react
npm install -D eslint-plugin-react-hooks
npm install -D eslint-plugin-react-refresh
npm install -D eslint-plugin-jsx-a11y
```

---

## **4. CONFIGURATION PRETTIER**

### **4.1 Configuration Prettier**

**Fichier `.prettierrc` obligatoire :**
```json
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid"
}
```

**Fichier `.prettierignore` :**
```
dist
node_modules
.env.local
*.md
```

### **4.2 Installation Prettier**

```bash
npm install -D prettier
npm install -D eslint-config-prettier
npm install -D eslint-plugin-prettier
```

---

## **5. CONFIGURATION GIT**

### **5.1 Fichier .gitignore Obligatoire**

**Contenu `.gitignore` :**
```
# Dépendances
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build
dist/
build/

# Environnement
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Runtime
pids
*.pid
*.seed
*.pid.lock

# Coverage
coverage/
.nyc_output

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log
```

### **5.2 Hooks Git (Husky)**

**Installation et configuration :**
```bash
npm install -D husky
npm install -D lint-staged
npx husky install
npx husky add .husky/pre-commit "npx lint-staged"
```

**Configuration `package.json` :**
```json
{
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{json,css,md}": [
      "prettier --write"
    ]
  }
}
```

---

## **6. SCRIPTS PACKAGE.JSON**

### **6.1 Scripts Obligatoires**

**Section scripts dans `package.json` :**
```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "format": "prettier --write \"src/**/*.{ts,tsx,json,css,md}\"",
    "format:check": "prettier --check \"src/**/*.{ts,tsx,json,css,md}\"",
    "type-check": "tsc --noEmit",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "analyze": "vite-bundle-analyzer"
  }
}
```

---

## **7. CONFIGURATION TESTING**

### **7.1 Configuration Vitest**

**Installation :**
```bash
npm install -D vitest
npm install -D @testing-library/react
npm install -D @testing-library/jest-dom
npm install -D @testing-library/user-event
npm install -D jsdom
```

**Fichier `vitest.config.ts` :**
```typescript
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
})
```

**Fichier `src/test/setup.ts` :**
```typescript
import '@testing-library/jest-dom'
```

---

## **8. CONFIGURATION VS CODE**

### **8.1 Settings Workspace**

**Fichier `.vscode/settings.json` :**
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  }
}
```

### **8.2 Extensions Recommandées**

**Fichier `.vscode/extensions.json` :**
```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

---

## **9. VALIDATION ET TESTS**

### **9.1 Checklist de Configuration**

**Vérifications obligatoires :**
- [ ] Vite.js configuré avec plugins essentiels
- [ ] TypeScript strict activé et configuré
- [ ] ESLint configuré avec règles strictes
- [ ] Prettier configuré et intégré
- [ ] Git hooks (Husky) fonctionnels
- [ ] Scripts npm/yarn opérationnels
- [ ] Configuration de test (Vitest) prête
- [ ] VS Code configuré avec extensions

### **9.2 Tests de Validation**

**Commandes de test :**
```bash
# Vérification TypeScript
npm run type-check

# Vérification ESLint
npm run lint

# Vérification Prettier
npm run format:check

# Build de production
npm run build

# Tests unitaires
npm run test
```

---

**⚠️ IMPORTANT :** Cette configuration est la base technique obligatoire pour tous les projets. Aucune dérogation n'est autorisée sans validation explicite de Cisco. L'agent doit s'assurer que tous les outils fonctionnent correctement avant de passer à la phase de développement.
