---
type: auto
description: "Performance et optimisation - React, 3D, assets et Core Web Vitals"
---

# PERFORMANCE ET OPTIMISATION

## OPTIMISATIONS REACT OBLIGATOIRES

### Hooks d'Optimisation
- **React.memo** pour éviter les re-rendus inutiles des composants
- **useCallback** pour mémoriser les fonctions passées en props
- **useMemo** pour mémoriser les calculs coûteux
- **useTransition** pour les mises à jour non urgentes

### Code Splitting et Lazy Loading
```typescript
// Lazy loading des composants
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// Utilisation avec Suspense
<Suspense fallback={<div>Chargement...</div>}>
  <LazyComponent />
</Suspense>

// Code splitting par routes
const HomePage = React.lazy(() => import('./pages/HomePage'));
const AboutPage = React.lazy(() => import('./pages/AboutPage'));
```

### Optimisation des Re-rendus
```typescript
// Mémorisation des fonctions
const handleClick = useCallback((id: string) => {
  setSelectedId(id);
}, []);

// Mémorisation des calculs coûteux
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// Mémorisation des composants
const MemoizedComponent = React.memo(({ data, onUpdate }) => {
  return <div>{data.name}</div>;
});
```

## OPTIMISATIONS DES ASSETS

### Images et Médias
- **Formats optimisés** (WebP, AVIF)
- **Lazy loading des images** obligatoire
- **Responsive images** avec `srcset`
- **Compression appropriée** avant intégration

### Pattern d'Image Optimisée
```typescript
// Image responsive avec lazy loading
<img
  src="image-small.webp"
  srcSet="image-small.webp 480w, image-medium.webp 768w, image-large.webp 1200w"
  sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, 33vw"
  loading="lazy"
  alt="Description en français"
/>
```

### Bundles JavaScript
- **Code splitting par routes** obligatoire
- **Tree shaking activé** dans la configuration Vite
- **Analyse des bundles** avec `vite-bundle-analyzer`
- **Élimination du code mort** systématique

## PERFORMANCE 3D (THREE.JS/R3F)

### Optimisations Obligatoires
- **Instancing** pour les objets répétés
- **LOD (Level of Detail)** pour les modèles complexes
- **Frustum culling** et **occlusion culling**
- **Limitation du nombre de draw calls**
- **Optimisation des shaders et textures**

### Pattern d'Optimisation 3D
```typescript
// Instancing pour objets répétés
import { useRef } from 'react';
import { InstancedMesh } from 'three';

const InstancedObjects = ({ count = 1000 }) => {
  const meshRef = useRef<InstancedMesh>(null);

  useEffect(() => {
    if (!meshRef.current) return;

    // Configuration des instances
    for (let i = 0; i < count; i++) {
      const matrix = new Matrix4();
      matrix.setPosition(
        Math.random() * 100 - 50,
        Math.random() * 100 - 50,
        Math.random() * 100 - 50
      );
      meshRef.current.setMatrixAt(i, matrix);
    }
    meshRef.current.instanceMatrix.needsUpdate = true;
  }, [count]);

  return (
    <instancedMesh ref={meshRef} args={[null, null, count]}>
      <boxGeometry />
      <meshStandardMaterial />
    </instancedMesh>
  );
};
```

### LOD (Level of Detail)
```typescript
// Implémentation LOD pour modèles complexes
import { LOD } from 'three';

const OptimizedModel = ({ distance }) => {
  const lodRef = useRef<LOD>(null);

  useEffect(() => {
    if (!lodRef.current) return;

    // Modèle haute définition (proche)
    lodRef.current.addLevel(highDetailMesh, 0);
    // Modèle moyenne définition (moyen)
    lodRef.current.addLevel(mediumDetailMesh, 50);
    // Modèle basse définition (loin)
    lodRef.current.addLevel(lowDetailMesh, 100);
  }, []);

  return <primitive ref={lodRef} object={new LOD()} />;
};
```

## CORE WEB VITALS

### Métriques Obligatoires à Surveiller
- **LCP (Largest Contentful Paint)** < 2.5s
- **FID (First Input Delay)** < 100ms
- **CLS (Cumulative Layout Shift)** < 0.1

### Optimisations pour LCP
```typescript
// Préchargement des ressources critiques
<link rel="preload" href="/fonts/main.woff2" as="font" type="font/woff2" crossOrigin="" />
<link rel="preload" href="/images/hero.webp" as="image" />

// Lazy loading pour contenu non critique
const HeavyComponent = React.lazy(() => import('./HeavyComponent'));
```

### Optimisations pour CLS
```css
/* Réserver l'espace pour les images */
.image-container {
  aspect-ratio: 16 / 9;
  width: 100%;
}

/* Éviter les changements de layout */
.skeleton-loader {
  height: 200px; /* Hauteur fixe pendant le chargement */
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
}
```

## MONITORING DES PERFORMANCES

### Outils de Mesure Obligatoires
- **Lighthouse** pour les audits automatisés
- **Web Vitals** pour le monitoring en temps réel
- **React DevTools Profiler** pour l'analyse des composants

### Implémentation du Monitoring
```typescript
// Monitoring des Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

const sendToAnalytics = (metric) => {
  // Envoyer les métriques à votre service d'analytics
  console.log('Performance metric:', metric);
};

// Mesurer toutes les métriques importantes
getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

## OPTIMISATIONS TAILWIND CSS

### Configuration Optimisée
- **Purge CSS** activé pour éliminer les classes inutilisées
- **JIT (Just-In-Time)** pour la compilation à la demande
- **Classes utilitaires** privilégiées à 95%
- **Directive @apply** uniquement pour les patterns récurrents complexes

### Pattern d'Optimisation CSS
```typescript
// Composant stylisé dédié pour maintenir la lisibilité
const OptimizedCard = ({ children, className = '' }) => {
  return (
    <div className={`
      bg-white rounded-lg shadow-md p-6 
      hover:shadow-lg transition-shadow duration-200
      ${className}
    `}>
      {children}
    </div>
  );
};
```
