<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Index des Pages - Flexodiv Vitrine</title>
    <!-- Favicon classique -->
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="Flexodiv" />
    <link rel="manifest" href="/site.webmanifest" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827;
            color: #f3f4f6;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 60px auto;
            background: rgba(31,41,55,0.7);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 8px 32px 0 rgba(0,0,0,0.37);
        }
        h1 {
            text-align: center;
            font-size: 2.2rem;
            margin-bottom: 2rem;
            color: #4f46e5;
        }
        ul {
            list-style: none;
            padding: 0;
        }
        li {
            margin: 1.2rem 0;
        }
        a {
            color: #ec4899;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            transition: color 0.2s;
        }
        a:hover {
            color: #4f46e5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Index des Pages</h1>
        <ul>
            <li><a href="GitHub-Copilot-Pro-Plus.html">Guide Stratégique GitHub Copilot Pro Plus</a></li>
            <!-- Ajoute ici d'autres liens vers tes pages HTML -->
        </ul>
    </div>
</body>
</html>
