
import React, { useState, useRef, useEffect } from 'react';
import { ChatMessage } from '../types';
import { streamChatResponse } from '../services/geminiService';
import '../css/glowing-effect.css';

const SendIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
        <path d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z" />
    </svg>
);

const LoadingDots: React.FC = () => (
  <div className="flex items-center space-x-1">
    <div className="w-2 h-2 bg-brand-light rounded-full animate-bounce [animation-delay:-0.3s]"></div>
    <div className="w-2 h-2 bg-brand-light rounded-full animate-bounce [animation-delay:-0.15s]"></div>
    <div className="w-2 h-2 bg-brand-light rounded-full animate-bounce"></div>
  </div>
);

const AIChat: React.FC = () => {
    const [messages, setMessages] = useState<ChatMessage[]>([
        { sender: 'ai', text: "Bonjour ! Je suis l'assistant de Francisco. Posez-moi vos questions sur ses compétences et son expérience professionnelle." }
    ]);
    const [input, setInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const chatContainerRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        setTimeout(() => {
            if (messagesEndRef.current) {
                const container = messagesEndRef.current.parentElement;
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            }
        }, 100);
    };

    useEffect(() => {
        if (!isLoading) {
            scrollToBottom();
        }
    }, [messages, isLoading]);

    // Empêcher la propagation du scroll vers la page
    useEffect(() => {
        const chatContainer = chatContainerRef.current;
        if (!chatContainer) return;

        const handleWheel = (e: WheelEvent) => {
            const { scrollTop, scrollHeight, clientHeight } = chatContainer;
            const isScrollingUp = e.deltaY < 0;
            const isScrollingDown = e.deltaY > 0;
            const isAtTop = scrollTop === 0;
            const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;

            // Empêcher la propagation seulement si on peut encore scroller dans le chat
            if ((isScrollingUp && !isAtTop) || (isScrollingDown && !isAtBottom)) {
                e.stopPropagation();
            }
        };

        chatContainer.addEventListener('wheel', handleWheel, { passive: false });
        return () => chatContainer.removeEventListener('wheel', handleWheel);
    }, []);

    const handleSend = async (prompt?: string) => {
        const messageText = prompt || input;
        if (!messageText.trim() || isLoading) return;

        console.log('🚀 Envoi du message:', messageText);

        const newUserMessage: ChatMessage = { sender: 'user', text: messageText };
        setMessages(prev => [...prev, newUserMessage]);
        setInput('');
        setIsLoading(true);

        // Ajouter un message vide pour l'IA qui sera rempli progressivement
        setMessages(prev => [...prev, { sender: 'ai', text: '' }]);

        try {
            await streamChatResponse(messageText, (chunk) => {
                setMessages(prev => {
                    const lastMessage = prev[prev.length - 1];
                    if (lastMessage.sender === 'ai') {
                        const updatedMessage = { ...lastMessage, text: lastMessage.text + chunk };
                        return [...prev.slice(0, -1), updatedMessage];
                    }
                    return prev;
                });
            });
        } catch (error) {
            console.error('❌ Erreur dans handleSend:', error);
            setMessages(prev => [...prev.slice(0, -1), { sender: 'ai', text: 'Désolé, une erreur est survenue. Veuillez réessayer plus tard.' }]);
        }

        setIsLoading(false);
    };

    const suggestions = [
        "Quelle est ton expérience en développement frontend ?",
        "Comment fonctionne l'ingénierie du prompt ?",
        "Travailles-tu avec GSAP & Three.js ?",
    ];

    return (
        <div className="neon-card h-full flex flex-col allow-scroll">
            <h3 className="text-xl font-bold mb-4 text-white">Assistant IA FlexoDiv</h3>
            <div ref={chatContainerRef} className="flex-grow pr-2 space-y-4 mb-4 h-[400px] chat-scroll">
                {messages.map((msg, index) => (
                    <div key={index} className={`flex items-end gap-2 ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                        {msg.sender === 'ai' && <div className="w-8 h-8 rounded-full bg-gradient-to-br from-brand-blue to-brand-purple flex-shrink-0"></div>}
                        <div className={`max-w-xs md:max-w-md p-3 rounded-lg ${msg.sender === 'user' ? 'bg-brand-blue text-white' : 'bg-brand-surface text-brand-light'}`}>
                            {msg.sender === 'ai' && msg.text === '' && isLoading ? <LoadingDots /> : <p className="whitespace-pre-wrap">{msg.text}</p>}
                        </div>
                    </div>
                ))}
                <div ref={messagesEndRef} />
            </div>

            <div className="mt-auto">
                <div className="border-t border-brand-surface pt-4">
                    <p className="text-sm text-brand-muted mb-2">Suggestions de questions :</p>
                    <div className="flex flex-wrap gap-2 mb-4">
                        {suggestions.map((s, i) => (
                            <button
                                key={i}
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    handleSend(s);
                                }}
                                className="bg-brand-surface hover:bg-brand-blue/50 text-xs text-brand-light px-3 py-1 rounded-full transition-colors disabled:opacity-50"
                                disabled={isLoading}
                            >
                                {s}
                            </button>
                        ))}
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    <input
                        type="text"
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSend()}
                        placeholder="Posez une question sur mes compétences..."
                        className="w-full bg-brand-surface rounded-lg p-3 text-brand-light placeholder-brand-muted focus:outline-none focus:ring-2 focus:ring-brand-purple"
                        disabled={isLoading}
                    />
                    <button onClick={() => handleSend()} disabled={isLoading || !input.trim()} className="bg-brand-blue text-white p-3 rounded-lg hover:bg-brand-purple transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <SendIcon />
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AIChat;
