import React, { useRef, useEffect } from 'react';

interface Particle {
    x: number;
    y: number;
    vx: number;
    vy: number;
    size: number;
    opacity: number;
    life: number;
    maxLife: number;
    type: 'dot' | 'code';
    codeText?: string;
    color: string;
}

const CodeParticlesBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let animationId: number;
        let particles: Particle[] = [];
        const maxParticles = 150;
        let mouseX = 0;
        let mouseY = 0;
        const mouseInfluence = 100;

        // Fragments de code pour l'animation
        const codeFragments = [
            'React', 'TypeScript', 'GSAP', 'CSS', 'HTML', 'JS',
            'API', 'JSON', 'HTTP', 'DOM', 'UI', 'UX',
            '{ }', '[ ]', '( )', '=>', '&&', '||',
            '01', '10', '11', '00', '101', '010',
            'const', 'let', 'var', 'function', 'class', 'import'
        ];

        // Couleurs du gradient Gemini
        const geminiColors = [
            '#1A3452', '#2190F6', '#6689EF', '#8D86ED', '#AE87F3'
        ];

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            particles = [];
        };

        const createParticle = () => {
            const isCode = Math.random() < 0.3; // 30% de chance d'être du code
            const particle: Particle = {
                x: Math.random() * canvas.width,
                y: canvas.height + 50, // Commencer en bas de l'écran
                vx: (Math.random() - 0.5) * 1, // Réduit de 2 à 1
                vy: -Math.random() * 1.5 - 0.5, // Réduit de 3-1 à 1.5-0.5
                size: isCode ? Math.random() * 8 + 4 : Math.random() * 4 + 2,
                opacity: Math.random() * 0.8 + 0.2,
                life: 0,
                maxLife: Math.random() * 400 + 300, // Augmenté pour durée plus longue
                type: isCode ? 'code' : 'dot',
                codeText: isCode ? codeFragments[Math.floor(Math.random() * codeFragments.length)] : undefined,
                color: geminiColors[Math.floor(Math.random() * geminiColors.length)]
            };
            return particle;
        };

        const updateParticle = (particle: Particle) => {
            // Influence de la souris
            const dx = mouseX - particle.x;
            const dy = mouseY - particle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < mouseInfluence) {
                const force = (mouseInfluence - distance) / mouseInfluence;
                particle.vx += (dx / distance) * force * 0.03;
                particle.vy += (dy / distance) * force * 0.03;
            }

            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life++;

            // Effet de gravité inverse légère
            particle.vy -= 0.01;

            // Oscillation horizontale
            particle.vx += Math.sin(particle.life * 0.01) * 0.02;

            // Friction légère
            particle.vx *= 0.995;
            particle.vy *= 0.995;

            // Fade out en fin de vie
            const lifeRatio = particle.life / particle.maxLife;
            if (lifeRatio > 0.8) {
                particle.opacity *= 0.98;
            }

            return particle.life < particle.maxLife && particle.y > -50 && particle.opacity > 0.01;
        };

        const drawParticle = (particle: Particle) => {
            ctx.save();
            ctx.globalAlpha = particle.opacity;

            if (particle.type === 'dot') {
                // Dessiner une particule point avec lueur
                const gradient = ctx.createRadialGradient(
                    particle.x, particle.y, 0,
                    particle.x, particle.y, particle.size * 2
                );
                gradient.addColorStop(0, particle.color);
                gradient.addColorStop(0.5, particle.color);
                gradient.addColorStop(1, particle.color);

                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();

                // Effet de lueur supplémentaire
                if (Math.random() < 0.1) {
                    ctx.shadowColor = particle.color;
                    ctx.shadowBlur = 15;
                    ctx.fillStyle = particle.color;
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size * 0.5, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.shadowBlur = 0;
                }
            } else if (particle.type === 'code' && particle.codeText) {
                // Dessiner du texte de code
                ctx.font = `${particle.size}px 'Courier New', monospace`;
                ctx.fillStyle = particle.color;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';

                // Effet de lueur pour le texte
                ctx.shadowColor = particle.color;
                ctx.shadowBlur = 8;
                ctx.fillText(particle.codeText, particle.x, particle.y);
                ctx.shadowBlur = 0;

                // Texte principal plus net
                ctx.fillStyle = particle.color;
                ctx.fillText(particle.codeText, particle.x, particle.y);
            }

            ctx.restore();
        };

        const animate = () => {
            // Fond noir avec légère transparence pour l'effet de traînée
            ctx.fillStyle = 'rgba(0, 0, 0, 0.08)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Créer de nouvelles particules moins fréquemment
            if (particles.length < maxParticles && Math.random() < 0.15) {
                particles.push(createParticle());
            }

            // Mettre à jour et dessiner les particules
            particles = particles.filter(particle => {
                const alive = updateParticle(particle);
                if (alive) {
                    drawParticle(particle);
                }
                return alive;
            });

            // Effet de pluie de code binaire occasionnel
            if (Math.random() < 0.05) {
                const binaryRain = createParticle();
                binaryRain.type = 'code';
                binaryRain.codeText = Math.random() < 0.5 ? '0' : '1';
                binaryRain.x = Math.random() * canvas.width;
                binaryRain.y = -20;
                binaryRain.vy = Math.random() * 2 + 1;
                binaryRain.vx = 0;
                binaryRain.size = Math.random() * 6 + 8;
                binaryRain.color = geminiColors[Math.floor(Math.random() * geminiColors.length)];
                particles.push(binaryRain);
            }

            animationId = requestAnimationFrame(animate);
        };

        initialize();
        animate();

        const handleResize = () => {
            initialize();
        };

        const handleMouseMove = (event: MouseEvent) => {
            mouseX = event.clientX;
            mouseY = event.clientY;
        };

        window.addEventListener('resize', handleResize);
        window.addEventListener('mousemove', handleMouseMove);

        return () => {
            cancelAnimationFrame(animationId);
            window.removeEventListener('resize', handleResize);
            window.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas ref={canvasRef} />
        </div>
    );
};

export default CodeParticlesBackground;
