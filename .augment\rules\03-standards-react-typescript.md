---
type: auto
description: "Standards de code React/TypeScript - Hooks, composants et bonnes pratiques"
---

# STANDARDS REACT/TYPESCRIPT

## QUALITÉ & ROBUSTESSE

### TypeScript Strict
- **Typer rigoureusement** toutes les props, les états et les retours de fonction
- **L'utilisation de `any` est interdite** sans justification documentée
- Utiliser des interfaces et types explicites pour toutes les données

### Gestion d'Erreurs Obligatoire
- **TOUJOURS** considérer les cas d'erreur (appels API, interactions utilisateur)
- Implémenter une gestion appropriée des erreurs à tous les niveaux

## HOOKS REACT OBLIGATOIRES

### Hooks d'État et d'Effets
- **Utiliser les Hooks React** (useState, useEffect, etc.) pour toute la logique d'état et d'effets
- **Composants fonctionnels uniquement** - pas de composants de classe

### Pattern de Gestion d'Erreurs
```typescript
// Gestion d'erreurs pour appels API
try {
  const response = await apiCall();
  return response.data;
} catch (error) {
  console.error('Erreur API:', error);
  throw new Error('Une erreur est survenue lors du chargement des données');
}

// Gestion d'erreurs dans les composants React
const [error, setError] = useState<string | null>(null);
const [loading, setLoading] = useState(false);

const handleAction = async () => {
  try {
    setLoading(true);
    setError(null);
    await performAction();
  } catch (err) {
    setError('Action impossible à réaliser');
  } finally {
    setLoading(false);
  }
};
```

## OPTIMISATIONS REACT OBLIGATOIRES

### Hooks d'Optimisation
- **React.memo** pour éviter les re-rendus inutiles des composants
- **useCallback** pour mémoriser les fonctions passées en props
- **useMemo** pour mémoriser les calculs coûteux
- **useTransition** pour les mises à jour non urgentes

### Code Splitting et Lazy Loading
```typescript
// Lazy loading des composants
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// Utilisation avec Suspense
<Suspense fallback={<div>Chargement...</div>}>
  <LazyComponent />
</Suspense>
```

## INTÉGRATIONS TECHNOLOGIQUES CRITIQUES

### React & GSAP
- **OBLIGATION** d'utiliser le hook `useGSAP()` pour toute intégration GSAP
- Assurer le nettoyage automatique des animations pour éviter les fuites mémoire
- Gérer correctement le cycle de vie des animations dans les composants React

### React Three Fiber (R3F)
- Optimiser systématiquement les scènes 3D
- Utiliser l'instancing pour les objets répétés
- Implémenter le lazy loading pour les modèles 3D
- Appliquer les LODs (Levels of Detail) pour maintenir les performances
- Limiter le nombre de draw calls et optimiser les shaders

## LAYOUT ET ALIGNEMENT (STANDARDS OBLIGATOIRES)

### Interdictions Formelles
- ❌ **JAMAIS** de `margin` ou `position` pour aligner des listes d'éléments
- ❌ **JAMAIS** de positionnement manuel pour les grilles de contenu
- ❌ **JAMAIS** de débordement horizontal (scrollbar horizontale)

### Obligations Techniques
- ✅ **TOUJOURS** utiliser Flexbox ou Grid pour les layouts
- ✅ **TOUJOURS** utiliser les utilitaires Tailwind (`flex`, `grid`, `gap-*`)
- ✅ **TOUJOURS** tester la responsivité sur tous les breakpoints

### Template de Grille Responsive Obligatoire
```html
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
  {/* Contenu des cartes */}
</div>
```

### Gestion des Débordements
- Utiliser `overflow-x-hidden` sur le conteneur principal si nécessaire
- Implémenter des breakpoints appropriés pour éviter les débordements
- Tester systématiquement sur mobile, tablette et desktop

## VALIDATION DES DONNÉES

### Validation Côté Client Obligatoire
- Valider tous les inputs utilisateur avant soumission
- Utiliser des schémas de validation (Zod recommandé)
- Afficher des messages d'erreur contextuels
- Prévenir les injections et attaques XSS

## ÉTATS DE CHARGEMENT ET FEEDBACK UTILISATEUR

### Implémentation Obligatoire
- États de loading pour toutes les opérations asynchrones
- Indicateurs visuels de progression
- Messages de succès/échec appropriés
- Gestion des timeouts et retry automatique
