 # **03_MONITORING-ET-LOGS.MD - SURVEILLANCE ET LOGGING**

## **OBJECTIF DU MONITORING**

Ce document définit la stratégie complète de surveillance, logging et alertes pour maintenir la visibilité sur l'état de l'application en production. Un monitoring efficace permet de détecter et résoudre les problèmes avant qu'ils n'impactent les utilisateurs.

---

## **1. STRATÉGIE DE MONITORING**

### **1.1 Niveaux de Surveillance**

**Hiérarchie de monitoring obligatoire :**
- **Infrastructure** : Serveurs, réseau, CDN (Netlify/Firebase)
- **Application** : Performance, erreurs, disponibilité
- **Métier** : Conversions, parcours utilisateur, KPIs
- **Sécurité** : Tentatives d'intrusion, anomalies d'accès

### **1.2 Métriques Clés (KPIs)**

**Métriques techniques obligatoires :**
- **Disponibilité** : Uptime > 99.9%
- **Performance** : Temps de réponse < 2s
- **Erreurs** : Taux d'erreur < 1%
- **Capacité** : Utilisation des ressources < 80%

**Métriques utilisateur :**
- **Core Web Vitals** : LCP, FID, CLS
- **Satisfaction** : Taux de rebond, temps de session
- **Conversion** : Funnel d'acquisition, rétention

---

## **2. OUTILS DE MONITORING**

### **2.1 Google Analytics 4**

**Configuration GA4 obligatoire :**
```typescript
// src/services/analytics.ts
interface AnalyticsEvent {
  event_name: string
  event_parameters?: Record<string, any>
}

class AnalyticsService {
  private isInitialized = false

  init() {
    if (typeof window === 'undefined' || this.isInitialized) return

    // Chargement du script GA4
    const script = document.createElement('script')
    script.async = true
    script.src = `https://www.googletagmanager.com/gtag/js?id=${import.meta.env.VITE_GA4_MEASUREMENT_ID}`
    document.head.appendChild(script)

    // Configuration GA4
    window.dataLayer = window.dataLayer || []
    window.gtag = function() {
      window.dataLayer.push(arguments)
    }

    window.gtag('js', new Date())
    window.gtag('config', import.meta.env.VITE_GA4_MEASUREMENT_ID, {
      page_title: document.title,
      page_location: window.location.href,
      // Respect de la vie privée
      anonymize_ip: true,
      allow_google_signals: false,
      allow_ad_personalization_signals: false
    })

    this.isInitialized = true
  }

  // Suivi des pages
  trackPageView(page_title: string, page_location: string) {
    if (!this.isInitialized) return

    window.gtag('config', import.meta.env.VITE_GA4_MEASUREMENT_ID, {
      page_title,
      page_location
    })
  }

  // Suivi des événements
  trackEvent(event: AnalyticsEvent) {
    if (!this.isInitialized) return

    window.gtag('event', event.event_name, event.event_parameters)
  }

  // Suivi des erreurs
  trackError(error: Error, context?: string) {
    this.trackEvent({
      event_name: 'exception',
      event_parameters: {
        description: error.message,
        fatal: false,
        context: context || 'unknown'
      }
    })
  }

  // Suivi des performances
  trackPerformance(metric_name: string, value: number) {
    this.trackEvent({
      event_name: 'performance_metric',
      event_parameters: {
        metric_name,
        value: Math.round(value)
      }
    })
  }
}

export const analytics = new AnalyticsService()
```

### **2.2 Web Vitals Monitoring**

**Surveillance des Core Web Vitals :**
```typescript
// src/services/webVitals.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'
import { analytics } from './analytics'

interface WebVitalMetric {
  name: string
  value: number
  id: string
  delta: number
}

class WebVitalsService {
  private metrics: Map<string, WebVitalMetric> = new Map()

  init() {
    // Core Web Vitals
    getCLS(this.handleMetric.bind(this))
    getFID(this.handleMetric.bind(this))
    getLCP(this.handleMetric.bind(this))

    // Autres métriques importantes
    getFCP(this.handleMetric.bind(this))
    getTTFB(this.handleMetric.bind(this))
  }

  private handleMetric(metric: WebVitalMetric) {
    // Stocker la métrique
    this.metrics.set(metric.name, metric)

    // Envoyer à Google Analytics
    analytics.trackPerformance(metric.name, metric.value)

    // Log pour debugging
    console.log(`${metric.name}: ${metric.value}`)

    // Alertes pour les métriques critiques
    this.checkThresholds(metric)
  }

  private checkThresholds(metric: WebVitalMetric) {
    const thresholds = {
      LCP: 2500, // ms
      FID: 100,  // ms
      CLS: 0.1,  // score
      FCP: 1800, // ms
      TTFB: 600  // ms
    }

    const threshold = thresholds[metric.name as keyof typeof thresholds]
    if (threshold && metric.value > threshold) {
      console.warn(`⚠️ ${metric.name} dépasse le seuil: ${metric.value} > ${threshold}`)

      // Envoyer une alerte
      analytics.trackEvent({
        event_name: 'performance_alert',
        event_parameters: {
          metric_name: metric.name,
          value: metric.value,
          threshold,
          severity: 'warning'
        }
      })
    }
  }

  getMetrics(): Record<string, WebVitalMetric> {
    return Object.fromEntries(this.metrics)
  }
}

export const webVitals = new WebVitalsService()
```

### **2.3 Error Monitoring**

**Service de monitoring des erreurs :**
```typescript
// src/services/errorMonitoring.ts
interface ErrorReport {
  message: string
  stack?: string
  url: string
  line?: number
  column?: number
  timestamp: Date
  userAgent: string
  userId?: string
  context?: Record<string, any>
}

class ErrorMonitoringService {
  private errorQueue: ErrorReport[] = []
  private maxQueueSize = 100

  init() {
    // Capture des erreurs JavaScript globales
    window.addEventListener('error', this.handleError.bind(this))

    // Capture des promesses rejetées
    window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this))

    // Capture des erreurs React (via Error Boundary)
    this.setupReactErrorHandler()
  }

  private handleError(event: ErrorEvent) {
    const errorReport: ErrorReport = {
      message: event.message,
      stack: event.error?.stack,
      url: event.filename,
      line: event.lineno,
      column: event.colno,
      timestamp: new Date(),
      userAgent: navigator.userAgent
    }

    this.reportError(errorReport)
  }

  private handlePromiseRejection(event: PromiseRejectionEvent) {
    const errorReport: ErrorReport = {
      message: `Unhandled Promise Rejection: ${event.reason}`,
      stack: event.reason?.stack,
      url: window.location.href,
      timestamp: new Date(),
      userAgent: navigator.userAgent
    }

    this.reportError(errorReport)
  }

  private setupReactErrorHandler() {
    // Intégration avec Error Boundary
    window.__REACT_ERROR_HANDLER__ = (error: Error, errorInfo: any) => {
      const errorReport: ErrorReport = {
        message: error.message,
        stack: error.stack,
        url: window.location.href,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        context: {
          componentStack: errorInfo.componentStack,
          errorBoundary: true
        }
      }

      this.reportError(errorReport)
    }
  }

  reportError(errorReport: ErrorReport) {
    // Ajouter à la queue
    this.errorQueue.push(errorReport)

    // Limiter la taille de la queue
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }

    // Envoyer à Google Analytics
    analytics.trackError(new Error(errorReport.message), errorReport.context?.errorBoundary ? 'react' : 'javascript')

    // Log pour debugging
    console.error('Error reported:', errorReport)

    // Envoyer au serveur (si endpoint disponible)
    this.sendToServer(errorReport)
  }

  private async sendToServer(errorReport: ErrorReport) {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorReport)
      })
    } catch (error) {
      console.error('Failed to send error report:', error)
    }
  }

  getErrorQueue(): ErrorReport[] {
    return [...this.errorQueue]
  }

  clearErrorQueue() {
    this.errorQueue = []
  }
}

export const errorMonitoring = new ErrorMonitoringService()
```

---

## **3. LOGGING STRUCTURÉ**

### **3.1 Service de Logging**

**Implémentation du service de logs :**
```typescript
// src/services/logger.ts
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

interface LogEntry {
  level: LogLevel
  message: string
  timestamp: Date
  context?: Record<string, any>
  userId?: string
  sessionId?: string
  url?: string
}

class LoggerService {
  private logs: LogEntry[] = []
  private maxLogs = 1000
  private currentLogLevel = LogLevel.INFO

  constructor() {
    // Ajuster le niveau selon l'environnement
    if (import.meta.env.DEV) {
      this.currentLogLevel = LogLevel.DEBUG
    }
  }

  private createLogEntry(level: LogLevel, message: string, context?: Record<string, any>): LogEntry {
    return {
      level,
      message,
      timestamp: new Date(),
      context,
      userId: this.getCurrentUserId(),
      sessionId: this.getSessionId(),
      url: window.location.href
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.currentLogLevel
  }

  private addLog(entry: LogEntry) {
    this.logs.push(entry)

    // Limiter la taille du buffer
    if (this.logs.length > this.maxLogs) {
      this.logs.shift()
    }

    // Envoyer les logs critiques immédiatement
    if (entry.level >= LogLevel.ERROR) {
      this.sendLogToServer(entry)
    }
  }

  debug(message: string, context?: Record<string, any>) {
    if (!this.shouldLog(LogLevel.DEBUG)) return

    const entry = this.createLogEntry(LogLevel.DEBUG, message, context)
    this.addLog(entry)
    console.debug(`[DEBUG] ${message}`, context)
  }

  info(message: string, context?: Record<string, any>) {
    if (!this.shouldLog(LogLevel.INFO)) return

    const entry = this.createLogEntry(LogLevel.INFO, message, context)
    this.addLog(entry)
    console.info(`[INFO] ${message}`, context)
  }

  warn(message: string, context?: Record<string, any>) {
    if (!this.shouldLog(LogLevel.WARN)) return

    const entry = this.createLogEntry(LogLevel.WARN, message, context)
    this.addLog(entry)
    console.warn(`[WARN] ${message}`, context)
  }

  error(message: string, error?: Error, context?: Record<string, any>) {
    const entry = this.createLogEntry(LogLevel.ERROR, message, {
      ...context,
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : undefined
    })

    this.addLog(entry)
    console.error(`[ERROR] ${message}`, error, context)
  }

  private getCurrentUserId(): string | undefined {
    // Récupérer l'ID utilisateur depuis le contexte d'auth
    return undefined // À implémenter selon votre système d'auth
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('sessionId')
    if (!sessionId) {
      sessionId = crypto.randomUUID()
      sessionStorage.setItem('sessionId', sessionId)
    }
    return sessionId
  }

  private async sendLogToServer(entry: LogEntry) {
    try {
      await fetch('/api/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(entry)
      })
    } catch (error) {
      console.error('Failed to send log to server:', error)
    }
  }

  getLogs(level?: LogLevel): LogEntry[] {
    return level !== undefined
      ? this.logs.filter(log => log.level >= level)
      : this.logs
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }
}

export const logger = new LoggerService()
```

### **3.2 Intégration dans l'Application**

**Initialisation des services de monitoring :**
```typescript
// src/main.tsx ou src/App.tsx
import { analytics } from './services/analytics'
import { webVitals } from './services/webVitals'
import { errorMonitoring } from './services/errorMonitoring'
import { logger } from './services/logger'

// Initialisation du monitoring
const initMonitoring = () => {
  // Analytics
  analytics.init()

  // Web Vitals
  webVitals.init()

  // Error Monitoring
  errorMonitoring.init()

  // Log de démarrage
  logger.info('Application started', {
    version: import.meta.env.VITE_APP_VERSION,
    environment: import.meta.env.MODE,
    timestamp: new Date().toISOString()
  })
}

// Appeler lors du démarrage de l'application
initMonitoring()
```

---

## **4. ALERTES ET NOTIFICATIONS**

### **4.1 Système d'Alertes**

**Configuration des seuils d'alerte :**
```typescript
// src/services/alerting.ts
interface AlertRule {
  name: string
  condition: (value: number) => boolean
  severity: 'info' | 'warning' | 'error' | 'critical'
  cooldown: number // minutes
}

class AlertingService {
  private alertRules: AlertRule[] = [
    {
      name: 'High Error Rate',
      condition: (errorRate) => errorRate > 5, // %
      severity: 'error',
      cooldown: 15
    },
    {
      name: 'Poor LCP Performance',
      condition: (lcp) => lcp > 2500, // ms
      severity: 'warning',
      cooldown: 30
    },
    {
      name: 'High Memory Usage',
      condition: (memoryUsage) => memoryUsage > 80, // %
      severity: 'warning',
      cooldown: 10
    }
  ]

  private lastAlerts: Map<string, Date> = new Map()

  checkAlert(ruleName: string, value: number) {
    const rule = this.alertRules.find(r => r.name === ruleName)
    if (!rule) return

    // Vérifier la condition
    if (!rule.condition(value)) return

    // Vérifier le cooldown
    const lastAlert = this.lastAlerts.get(ruleName)
    if (lastAlert) {
      const timeSinceLastAlert = Date.now() - lastAlert.getTime()
      if (timeSinceLastAlert < rule.cooldown * 60 * 1000) return
    }

    // Déclencher l'alerte
    this.triggerAlert(rule, value)
    this.lastAlerts.set(ruleName, new Date())
  }

  private triggerAlert(rule: AlertRule, value: number) {
    const alert = {
      rule: rule.name,
      severity: rule.severity,
      value,
      timestamp: new Date(),
      url: window.location.href
    }

    // Log l'alerte
    logger.warn(`Alert triggered: ${rule.name}`, alert)

    // Envoyer à Analytics
    analytics.trackEvent({
      event_name: 'alert_triggered',
      event_parameters: alert
    })

    // Notification utilisateur (si critique)
    if (rule.severity === 'critical') {
      this.showUserNotification(alert)
    }
  }

  private showUserNotification(alert: any) {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`Alerte ${alert.severity}`, {
        body: `${alert.rule}: ${alert.value}`,
        icon: '/favicon.ico'
      })
    }
  }
}

export const alerting = new AlertingService()
```

---

**⚠️ IMPORTANT :** Le monitoring et logging sont essentiels pour maintenir la qualité de service en production. Tous les services définis dans ce document DOIVENT être implémentés et configurés correctement pour assurer une surveillance efficace de l'application.
```