/**
 * Base de connaissances pour l'Assistant IA FlexoDiv
 * Contient toutes les informations sur Francisco (FlexoDiv), ses compétences, projets et services
 */

export const KNOWLEDGE_BASE = {
  // Informations personnelles et professionnelles
  profile: {
    name: "<PERSON>",
    alias: "FlexoDiv", 
    title: "Développeur de Solutions IA & Full-Stack Créatif",
    description: "Développeur créatif spécialisé dans l'intégration d'intelligence artificielle et le développement full-stack moderne",
    linkedin: "https://www.linkedin.com/in/flexodiv-développeur-de-solutions-ia-982582203/",
    github: "https://github.com/cisco-03",
    location: "France",
    languages: ["Français", "Anglais technique"]
  },

  // Compétences techniques principales
  skills: {
    frontend: {
      title: "Développement Frontend Moderne",
      description: "Construction d'interfaces web réactives, rapides et interactives avec une stack technique légère et à la pointe",
      technologies: ["React 18", "TypeScript", "Vite.js", "Tailwind CSS", "Alpine.js"],
      specialties: [
        "Prototypage rapide d'interfaces",
        "Composants web réutilisables", 
        "Intégration de designs complexes",
        "Interfaces responsives et performantes"
      ]
    },
    
    animations: {
      title: "Animations & Effets Visuels",
      description: "Création d'animations fluides et d'effets visuels immersifs pour des expériences utilisateur exceptionnelles",
      technologies: ["GSAP", "Three.js", "react-three-fiber", "Lenis", "CSS3"],
      specialties: [
        "Animations complexes avec GSAP",
        "Expériences 3D avec Three.js",
        "Défilement fluide et parallaxe",
        "Micro-interactions et transitions"
      ]
    },

    ai_integration: {
      title: "Intégration d'Assistants IA",
      description: "Intégration de chatbots et d'agents intelligents directement dans les outils et sites web",
      technologies: ["Google Gemini API", "OpenAI API", "MindStudio", "Context7"],
      specialties: [
        "Déploiement d'agents créés avec MindStudio",
        "Interfaces utilisateur conversationnelles",
        "Connexion à des bases de connaissances (Firestore)",
        "Support et formation utilisateur"
      ]
    },

    backend: {
      title: "Développement Backend",
      description: "Développement d'APIs robustes et de services backend performants",
      technologies: ["Node.js", "Express.js", "TypeScript", "Firebase", "MongoDB"],
      specialties: [
        "APIs RESTful sécurisées",
        "Authentification et autorisation",
        "Intégration de services tiers",
        "Optimisation des performances"
      ]
    }
  },

  // Stack techniques recommandées
  stacks: {
    light_interactive: {
      name: "Stack Légère & Interactive",
      description: "Idéal pour sites vitrines modernes, portfolios, landing pages animées",
      technologies: "Vite.js + Tailwind CSS + Alpine.js + GSAP + Lenis",
      use_cases: ["Sites vitrines", "Portfolios", "Landing pages", "Sites marketing"]
    },
    
    immersive_3d: {
      name: "Expérience 3D / Site Immersif", 
      frontend: "React + TypeScript + Three.js (via react-three-fiber) + GSAP + Tailwind CSS",
      backend: "Node.js + Express + TypeScript ou Firebase",
      deployment: "Vercel/Netlify (Frontend) + Render/Firebase (Backend)",
      use_cases: ["Expériences immersives", "Showcases 3D", "Applications interactives"]
    }
  },

  // Projets réalisés
  projects: [
    {
      title: "Assistant Parcours & Découverte",
      category: "IA",
      description: "Application d'optimisation de parcours pour livraisons et découvertes touristiques avec IA",
      technologies: ["React 18", "TypeScript", "Google Maps API", "Agent IA"],
      features: [
        "Assistants IA spécialisés (Livraison & Découverte)",
        "Assistant Cartographique IA conversationnel",
        "Optimisation intelligente des parcours",
        "Interface intuitive avec saisie vocale",
        "PWA avec géolocalisation"
      ],
      github: "https://github.com/cisco-03/Assistant-Parcours-Decouverte"
    },
    
    {
      title: "Developer Inspector Mode",
      category: "Outils de développement",
      description: "Module d'inspection et d'annotation assistée par IA pour développeurs",
      technologies: ["JavaScript ES6", "GSAP", "TailwindCSS", "Alpine.js", "Gemini 2.0 Flash"],
      features: [
        "Annotation assistée par IA via Gemini 2.0 Flash",
        "Enrichissement des prompts avec Context7",
        "Détection automatique de 25+ technologies",
        "Triple redondance de sauvegarde",
        "Export/Import des annotations"
      ]
    },

    {
      title: "Advisor Nutrition",
      category: "Frontend",
      description: "Application de gestion intelligente des listes de courses et planification nutritionnelle avec IA",
      technologies: ["JavaScript ES6", "Tailwind CSS", "OpenAI API"],
      features: [
        "Planification nutritionnelle intelligente",
        "Gestion automatisée des listes de courses",
        "Suggestions personnalisées par IA",
        "Interface responsive et intuitive"
      ]
    },

    {
      title: "FloraSynth",
      category: "Plateforme Créative IA",
      description: "Plateforme créative révolutionnaire avec IA pour la génération de contenu multimédia et l'assistance créative",
      status: "En développement",
      technologies: ["React 18", "Next.js 14", "TypeScript", "OpenAI GPT-4", "Stability AI", "Google Vision API"],
      features: [
        "Génération de contenu IA (textes, images, vidéos)",
        "Analyse visuelle avancée avec Google Vision API",
        "Évaluation créative par modèles ML personnalisés",
        "Interface intuitive avec animations Framer Motion",
        "Collaboration temps réel pour équipes créatives",
        "Export multi-format pour tous types de projets"
      ]
    }
  ],

  // Services proposés
  services: {
    ai_integration: {
      title: "Intégration d'Assistants IA",
      description: "Intégration complète d'agents conversationnels intelligents dans vos outils et sites web",
      deliverables: [
        "Configuration et déploiement d'agents IA",
        "Interface utilisateur conversationnelle",
        "Connexion aux bases de données existantes",
        "Formation et support utilisateur"
      ]
    },
    
    frontend_development: {
      title: "Développement Frontend Moderne",
      description: "Création d'interfaces web performantes et interactives",
      deliverables: [
        "Développement avec stack moderne (React/Vite/Tailwind)",
        "Animations et effets visuels avec GSAP",
        "Design responsive et accessible",
        "Optimisation des performances"
      ]
    },
    
    creative_solutions: {
      title: "Solutions Créatives & Innovation",
      description: "Développement de solutions sur mesure alliant créativité et technologie",
      deliverables: [
        "Prototypage rapide d'idées innovantes",
        "Intégration de technologies émergentes",
        "Expériences utilisateur immersives",
        "Conseil en architecture technique"
      ]
    }
  },

  // Packages d'assistance
  assistance_packages: {
    diagnostic: {
      name: "Diagnostic",
      price: "9,99€",
      description: "Analyse rapide de votre problème technique",
      paypal_id: "XTMX8UVUXL9C4"
    },
    repair: {
      name: "Réparation",
      price: "24,99€", 
      description: "Correction de bugs et problèmes techniques",
      paypal_id: "Y47VVWDF8XPKJ"
    },
    restoration: {
      name: "Restauration",
      price: "49,99€",
      description: "Remise en état complète de votre application",
      paypal_id: "WWUY2CZ88Y4K6"
    },
    complete_intervention: {
      name: "Intervention Complète",
      price: "99,99€",
      description: "Solution complète pour problèmes complexes",
      paypal_id: "GQHFAZ83BGMK4"
    },
    custom_intervention: {
      name: "Intervention Sur Mesure",
      price: "Devis personnalisé",
      description: "Solution entièrement personnalisée",
      paypal_id: "ZGJHPR2TU2A8G"
    }
  },

  // Approche et philosophie
  approach: {
    development_philosophy: "Code propre, stable et efficace avec focus sur l'expérience utilisateur",
    preferred_methods: [
      "Prototypage rapide et itératif",
      "Stack techniques légères et performantes", 
      "Intégration harmonieuse des technologies IA",
      "Design responsive-first",
      "Optimisation continue des performances"
    ],
    quality_standards: [
      "Zéro boucle infinie ou script instable",
      "Code clair, lisible et maintenable",
      "Gestion appropriée des cas d'erreur",
      "Tests unitaires et d'intégration",
      "Documentation technique complète"
    ]
  }
};

export default KNOWLEDGE_BASE;
