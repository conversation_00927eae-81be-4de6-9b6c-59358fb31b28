# 02_developpement-composants-react.md : Développement des Composants React

**Objectif :** Ce document guide l'agent de codage dans l'implémentation des composants React conformément à la structure et aux conventions définies.

**Directives pour l'agent de codage :**

1.  **Respect de la structure :**
    *   Tous les nouveaux composants doivent être créés et organisés selon les principes de la conception atomique (Atomic Design) ou la segmentation Smart/Dumb components, comme décrit dans le document `01_ARCHITECTURE_ET_SETUP/02_structure-composants-react.md`.
    *   Assurez-vous que chaque composant réside dans son propre dossier, contenant le fichier `.tsx` (ou `.ts` si le composant n'a pas de JSX), le fichier de style associé (si Tailwind ne suffit pas), et potentiellement un fichier de test.
2.  **Conventions de nommage :** Suivez les conventions de nommage React (PascalCase pour les composants, camelCase pour les fonctions et variables).
3.  **Props et typage :**
    *   Définissez clairement les types des props en utilisant TypeScript pour tous les composants.
    *   Privilégiez la déstructuration des props pour une meilleure lisibilité.
4.  **Gestion de l'état :**
    *   Utilisez les hooks d'état (`useState`, `useReducer`) de manière appropriée pour gérer l'état local des composants.
    *   Pour l'état global ou partagé, préparez-vous à utiliser un gestionnaire d'état que nous pourrions définir ultérieurement (ex: Context API, Redux, Zustand).
5.  **Effets de bord :** Gérez les effets de bord (appels API, abonnements, manipulations du DOM) à l'aide du hook `useEffect`, en veillant à nettoyer les ressources pour éviter les fuites de mémoire.
6.  **Optimisation des performances :**
    *   Utilisez `React.memo`, `useCallback`, `useMemo` lorsque c'est nécessaire pour optimiser le rendu des composants et éviter les re-rendus inutiles.
    *   Soyez attentif à la complexité des calculs au sein des rendus et déportez-les si possible.
7.  **Styles Tailwind CSS :**
    *   Appliquez le stylisme autant que possible via les classes utilitaires de Tailwind CSS, en vous référant à la configuration dans `01_ARCHITECTURE_ET_SETUP/03_configuration-tailwind.md`.
    *   N'utilisez des feuilles de style CSS modulaires ou des fichiers `.css` classiques qu'en dernier recours, pour des styles très spécifiques ou complexes non gérables par Tailwind.
8.  **Accessibilité (A11y) :** Intégrez les meilleures pratiques d'accessibilité dans la conception et l'implémentation des composants (ex: sémantique HTML, attributs `aria-`, gestion du focus).
9.  **Tests :**
    *   Écrivez des tests unitaires pour chaque composant, en utilisant des frameworks comme Jest ou React Testing Library.
    *   Assurez-vous de tester les comportements par défaut, les états et les interactions avec les composants.

---

## **COMPOSANT ANIMATION MATRIX - RÉFÉRENCE TECHNIQUE**

### **Composant MatrixRain - Code Complet**

```typescript
import React, { useRef, useEffect } from 'react';

// --- Définition des types pour la clarté en TypeScript ---
interface MatrixRainProps {
  // Aucune prop n'est nécessaire pour ce composant, mais c'est une bonne pratique de le définir.
}

// --- Le composant principal de l'animation ---
const MatrixRain: React.FC<MatrixRainProps> = () => {
  // useRef pour obtenir une référence directe à l'élément <canvas> du DOM
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // useEffect est utilisé pour exécuter le code de l'animation après le montage du composant.
  // Le tableau de dépendances vide [] assure que cet effet ne s'exécute qu'une seule fois.
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return; // Sécurité si le canvas n'est pas encore monté

    const ctx = canvas.getContext('2d');
    if (!ctx) return; // Sécurité si le contexte 2D n'est pas disponible

    // --- Configuration initiale ---
    let intervalId: number;

    // L'ensemble de caractères à utiliser, principalement des Katakana japonais comme dans le film.
    const katakana = 'アァカサタナハマヤャラワガザダバパイィキシチニヒミリヰギジヂビピウゥクスツヌフムユュルグズブヅプエェケセテネヘメレヱゲゼデベペオォコソトノホモヨョロヲゴゾドボポヴッン';
    const latin = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const nums = '0123456789';
    const alphabet = katakana + latin + nums;

    const fontSize = 16;
    let columns: number;
    let drops: number[];

    // Fonction pour initialiser ou réinitialiser les dimensions et les colonnes
    const initialize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;

      columns = Math.floor(canvas.width / fontSize);
      drops = [];
      for (let i = 0; i < columns; i++) {
        // Position de départ aléatoire pour un effet plus naturel au chargement
        drops[i] = Math.floor(Math.random() * (canvas.height / fontSize));
      }
    };

    // --- La boucle d'animation ---
    const draw = () => {
      // Le secret de l'effet de traînée : dessiner un rectangle noir semi-transparent
      // à chaque frame pour estomper lentement les caractères précédents.
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Création du dégradé de couleurs "Gemini"
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
      gradient.addColorStop(0, '#4285F4'); // Bleu Gemini
      gradient.addColorStop(0.5, '#9B59B6'); // Violet intermédiaire
      gradient.addColorStop(1, '#4A148C'); // Violet profond

      ctx.font = `${fontSize}px monospace`;

      // Parcourir chaque colonne pour dessiner les caractères
      for (let i = 0; i < drops.length; i++) {
        // Choisir un caractère aléatoire de notre alphabet
        const text = alphabet.charAt(Math.floor(Math.random() * alphabet.length));

        // --- NOUVEAU : Effet de lueur aléatoire ---
        // Une faible probabilité de rendre un caractère plus lumineux
        if (Math.random() > 0.99) {
          ctx.fillStyle = '#FFFFFF'; // Couleur de la lueur
          ctx.shadowColor = '#FFFFFF';
          ctx.shadowBlur = 10;
        } else {
          ctx.fillStyle = gradient; // Couleur normale
          ctx.shadowBlur = 0; // Pas de lueur
        }

        // Dessiner le caractère à la position x, y
        ctx.fillText(text, i * fontSize, drops[i] * fontSize);

        // Si la goutte atteint le bas de l'écran, la réinitialiser en haut
        // avec une probabilité aléatoire pour un effet moins uniforme.
        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }

        // Faire descendre la goutte
        drops[i]++;
      }
    };

    // --- Initialisation et gestion des événements ---
    initialize();

    // --- MODIFIÉ : Utilisation de setInterval pour ralentir l'animation ---
    // 50ms correspond à environ 20 images par seconde, ce qui est beaucoup plus lent.
    intervalId = window.setInterval(draw, 50);

    // Gérer le redimensionnement de la fenêtre pour que l'animation reste en plein écran
    const handleResize = () => {
      clearInterval(intervalId); // Arrêter l'ancienne animation
      initialize(); // Réinitialiser avec les nouvelles dimensions
      intervalId = window.setInterval(draw, 50); // Redémarrer l'animation
    };

    window.addEventListener('resize', handleResize);

    // --- Fonction de nettoyage ---
    // Cette fonction est retournée par useEffect et s'exécute lorsque le composant est démonté.
    // C'est crucial pour éviter les fuites de mémoire.
    return () => {
      clearInterval(intervalId);
      window.removeEventListener('resize', handleResize);
    };
  }, []); // Le tableau vide signifie que cet effet ne dépend d'aucune prop ou état.

  return (
    <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', background: '#000' }}>
      <canvas ref={canvasRef} />
    </div>
  );
};

// Exporter le composant pour l'utiliser dans d'autres parties de l'application.
// Dans un projet Create React App, vous pouvez remplacer le contenu de App.tsx par ceci.
export default function App() {
  return <MatrixRain />;
}
```

### **Caractéristiques Techniques**

**Performance :**
- Animation Canvas 2D native
- Gestion optimisée des événements resize
- Nettoyage automatique des listeners

**Responsive :**
- Adaptation automatique à toutes les tailles d'écran
- Recalcul dynamique des colonnes

**Effets Visuels :**
- Dégradé couleurs Gemini (bleu → violet → violet profond)
- Effet de lueur aléatoire sur certains caractères
- Traînée semi-transparente pour l'effet Matrix

**Intégration :**
- Compatible avec React 18+
- TypeScript strict
- Hooks modernes (useRef, useEffect)
- Gestion propre du cycle de vie