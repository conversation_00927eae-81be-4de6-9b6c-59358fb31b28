import React, { useState, useEffect, useRef } from 'react';

// --- Helper pour parser le texte ---
const FormattedText = ({ text }) => {
    if (!text) return null;
    const parts = text.split(/(`[^`]+`|\*\*.*?\*\*)/g).filter(Boolean);
    return (
        <span>
            {parts.map((part, i) => {
                if (part.startsWith('`')) {
                    return <code key={i} className="bg-black/20 text-blue-300 rounded-md px-2 py-1 text-sm font-mono">{part.slice(1, -1)}</code>;
                }
                if (part.startsWith('**')) {
                    return <strong key={i}>{part.slice(2, -2)}</strong>;
                }
                return part;
            })}
        </span>
    );
};

// --- Interfaces TypeScript ---
interface IStack { type: string; description: string; technologies: string; reason: string; }
interface IFullStack { useCase: string; frontend: string; backend: string; deployment: string; synergies: string; }

// --- Données ---
const stacksData: IStack[] = [
    { type: 'Stack "Légère & Interactive"', description: 'Idéal pour sites vitrines modernes, portfolios, landing pages animées', technologies: '`Vite.js` + `Tailwind CSS` + `Alpine.js` + `GSAP` + `Lenis`', reason: "C'est le combo parfait pour la performance et la créativité. **Vite** offre un serveur de dev ultra-rapide. **Tailwind** permet de styler sans quitter le HTML. **Alpine.js** ajoute l'interactivité. **GSAP** gère les animations complexes. On y ajoute **Lenis** pour un défilement fluide et professionnel, et le combo est parfait." },
    { type: 'Application Frontend (React)', description: 'Pour des applications web complexes, dashboards, plateformes SaaS', technologies: '`React` + `Vite.js` + `Tailwind CSS` + `Zustand` ou `Redux Toolkit`', reason: "C'est la stack la plus populaire pour les grosses applications. **React** construit l'interface utilisateur en composants. **Vite** compile le tout à la vitesse de l'éclair. **Tailwind** assure un design cohérent. **Zustand** (léger) ou **Redux** (plus complet) gère l'état global de l'application." },
    { type: 'Application Full-Stack "Serverless"', description: "Quand on a besoin d'un backend sans gérer de serveur", technologies: '`Stack Frontend au choix` (React/Vue/etc.) + `Firebase`', reason: "Cette approche est très moderne. Vous construisez votre frontend avec votre stack préférée, et **Firebase** s'occupe de tout le reste : base de données (Firestore), authentification des utilisateurs, stockage de fichiers et même hébergement." },
    { type: 'Application Full-Stack "Classique"', description: 'Pour des applications nécessitant un contrôle total du backend', technologies: '`React` + `Node.js/Express` + `PostgreSQL/MongoDB` + `Docker`', reason: "Cette stack donne un contrôle total. **React** pour le frontend, **Node.js/Express** pour créer des APIs robustes, **PostgreSQL** (relationnel) ou **MongoDB** (NoSQL) pour les données, et **Docker** pour déployer facilement partout." },
    { type: 'E-commerce Moderne', description: 'Pour boutiques en ligne performantes et évolutives', technologies: '`Next.js` + `Stripe` + `Sanity CMS` + `Vercel`', reason: "**Next.js** offre le rendu côté serveur pour le SEO. **Stripe** gère les paiements de façon sécurisée. **Sanity** permet de gérer le contenu facilement. **Vercel** déploie automatiquement. C'est la stack e-commerce moderne par excellence." },
    { type: 'Application Mobile Cross-Platform', description: 'Une seule base de code pour iOS et Android', technologies: '`React Native` + `Expo` + `Firebase`', reason: "**React Native** permet d'écrire une seule fois et de déployer sur iOS et Android. **Expo** simplifie énormément le développement et les tests. **Firebase** fournit le backend. C'est le moyen le plus efficace de créer des apps mobiles." },
    { type: 'Site Web Ultra-Rapide (JAMstack)', description: 'Pour des sites de contenu avec performance maximale', technologies: '`Astro` + `Markdown/MDX` + `Tailwind CSS` + `Netlify`', reason: "**Astro** génère des sites statiques ultra-rapides. **Markdown/MDX** permet d'écrire du contenu facilement. **Tailwind** assure le style. **Netlify** déploie et héberge gratuitement. Parfait pour blogs, documentations, sites corporate." },
    { type: 'Application Desktop Cross-Platform', description: 'Applications natives pour Windows, Mac et Linux', technologies: '`Electron` + `React/Vue` + `Node.js`', reason: "**Electron** permet de créer des apps desktop avec des technologies web. Vous utilisez **React** ou **Vue** pour l'interface, **Node.js** pour la logique. Des apps comme Discord, Slack, VS Code utilisent cette stack." },
    { type: 'API/Microservices Modernes', description: 'Backend scalable et maintenable', technologies: '`Node.js/Express` ou `Python/FastAPI` + `PostgreSQL` + `Redis` + `Docker`', reason: "Pour les APIs, **Node.js/Express** (JavaScript) ou **Python/FastAPI** sont excellents. **PostgreSQL** pour les données relationnelles, **Redis** pour le cache, **Docker** pour la containerisation. Stack robuste et scalable." },
    { type: 'Application Temps Réel', description: 'Chat, notifications live, collaboration', technologies: '`Socket.io` + `React` + `Node.js` + `MongoDB`', reason: "**Socket.io** gère la communication temps réel entre client et serveur. **React** pour l'interface réactive, **Node.js** pour le serveur, **MongoDB** pour stocker les données. Parfait pour chats, notifications, collaboration en temps réel." }
];

const fullStackCombinations: IFullStack[] = [
    { useCase: "Site Vitrine Moderne", frontend: "`Vite.js` + `Tailwind` + `Alpine.js`", backend: "Statique (pas de backend)", deployment: "`Netlify` ou `Vercel`", synergies: "Combo parfait pour la performance et l'interactivité. **Alpine.js** ajoute juste ce qu'il faut de JavaScript sans complexité." },
    { useCase: "Application SaaS Complète", frontend: "`React` + `Tailwind` + `Zustand`", backend: "`Node.js` + `Express` + `PostgreSQL`", deployment: "`Docker` + `AWS/DigitalOcean`", synergies: "Stack éprouvée pour les applications complexes. **Zustand** simplifie la gestion d'état, **PostgreSQL** assure la robustesse des données." },
    { useCase: "E-commerce Performant", frontend: "`Next.js` + `Tailwind`", backend: "`Sanity CMS` + `Stripe`", deployment: "`Vercel`", synergies: "**Next.js** optimise le SEO, **Sanity** facilite la gestion produits, **Stripe** sécurise les paiements. Déploiement automatique avec **Vercel**." },
    { useCase: "App Mobile + Web", frontend: "`React Native` (mobile) + `React` (web)", backend: "`Firebase`", deployment: "`App Stores` + `Netlify`", synergies: "Code partagé entre mobile et web. **Firebase** synchronise tout automatiquement. Une seule équipe peut gérer les deux plateformes." },
    { useCase: "Plateforme de Contenu", frontend: "`Astro` + `React Islands`", backend: "`Markdown` + `CMS Headless`", deployment: "`Netlify` + `CDN`", synergies: "**Astro** génère du HTML ultra-rapide, **React Islands** ajoute l'interactivité où nécessaire. Performance maximale pour le contenu." },
    { useCase: "Application Temps Réel", frontend: "`React` + `Socket.io-client`", backend: "`Node.js` + `Socket.io` + `MongoDB`", deployment: "`Heroku` ou `Railway`", synergies: "Communication bidirectionnelle instantanée. **MongoDB** stocke efficacement les données temps réel. Parfait pour chat, collaboration, gaming." }
];

const StacksTechnologiques: React.FC = () => {
    const [activeTab, setActiveTab] = useState<'stacks' | 'combinations'>('stacks');
    const [selectedStack, setSelectedStack] = useState<number | null>(null);
    const [selectedCombination, setSelectedCombination] = useState<number | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // Animation d'entrée
    useEffect(() => {
        if (containerRef.current) {
            containerRef.current.style.opacity = '0';
            containerRef.current.style.transform = 'translateY(20px)';
            
            const timer = setTimeout(() => {
                if (containerRef.current) {
                    containerRef.current.style.transition = 'all 0.6s ease-out';
                    containerRef.current.style.opacity = '1';
                    containerRef.current.style.transform = 'translateY(0)';
                }
            }, 100);
            
            return () => clearTimeout(timer);
        }
    }, []);

    return (
        <div ref={containerRef} className="max-w-6xl mx-auto p-6 space-y-8">
            {/* Header */}
            <div className="text-center space-y-4">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                    🚀 Stacks Technologiques Modernes
                </h1>
                <p className="text-gray-300 text-lg max-w-3xl mx-auto">
                    Découvrez les meilleures combinaisons de technologies pour vos projets, 
                    des sites vitrines aux applications complexes.
                </p>
            </div>

            {/* Navigation Tabs */}
            <div className="flex justify-center">
                <div className="bg-gray-800/50 rounded-lg p-1 flex">
                    <button
                        onClick={() => setActiveTab('stacks')}
                        className={`px-6 py-3 rounded-md font-medium transition-all duration-300 ${
                            activeTab === 'stacks'
                                ? 'bg-blue-600 text-white shadow-lg'
                                : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                        }`}
                    >
                        📚 Stacks Individuelles
                    </button>
                    <button
                        onClick={() => setActiveTab('combinations')}
                        className={`px-6 py-3 rounded-md font-medium transition-all duration-300 ${
                            activeTab === 'combinations'
                                ? 'bg-purple-600 text-white shadow-lg'
                                : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                        }`}
                    >
                        🔗 Combinaisons Full-Stack
                    </button>
                </div>
            </div>

            {/* Contenu des Stacks */}
            {activeTab === 'stacks' && (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {stacksData.map((stack, index) => (
                        <div
                            key={index}
                            className={`neon-card p-6 rounded-xl cursor-pointer transition-all duration-300 ${
                                selectedStack === index ? 'scale-105' : 'hover:scale-102'
                            }`}
                            onClick={() => setSelectedStack(selectedStack === index ? null : index)}
                        >
                            <div className="relative z-10">
                                <h3 className="text-xl font-bold text-white mb-3">
                                    {stack.type}
                                </h3>
                                <p className="text-gray-300 mb-4 text-sm">
                                    {stack.description}
                                </p>
                                <div className="mb-4">
                                    <FormattedText text={stack.technologies} />
                                </div>
                                
                                {selectedStack === index && (
                                    <div className="mt-4 pt-4 border-t border-gray-600">
                                        <h4 className="font-semibold text-blue-300 mb-2">💡 Pourquoi cette stack ?</h4>
                                        <p className="text-gray-300 text-sm leading-relaxed">
                                            <FormattedText text={stack.reason} />
                                        </p>
                                    </div>
                                )}
                                
                                <div className="mt-4 text-center">
                                    <span className="text-xs text-gray-400">
                                        {selectedStack === index ? '👆 Cliquez pour fermer' : '👆 Cliquez pour plus de détails'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Contenu des Combinaisons Full-Stack */}
            {activeTab === 'combinations' && (
                <div className="space-y-6">
                    {fullStackCombinations.map((combo, index) => (
                        <div
                            key={index}
                            className={`neon-card p-6 rounded-xl cursor-pointer transition-all duration-300 ${
                                selectedCombination === index ? 'scale-102' : 'hover:scale-101'
                            }`}
                            onClick={() => setSelectedCombination(selectedCombination === index ? null : index)}
                        >
                            <div className="relative z-10">
                                <div className="flex items-center justify-between mb-4">
                                    <h3 className="text-2xl font-bold text-white">
                                        🎯 {combo.useCase}
                                    </h3>
                                    <span className="text-xs text-gray-400">
                                        {selectedCombination === index ? '👆 Fermer' : '👆 Détails'}
                                    </span>
                                </div>
                                
                                <div className="grid md:grid-cols-3 gap-4 mb-4">
                                    <div>
                                        <h4 className="font-semibold text-green-300 mb-2">🎨 Frontend</h4>
                                        <FormattedText text={combo.frontend} />
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-blue-300 mb-2">⚙️ Backend</h4>
                                        <FormattedText text={combo.backend} />
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-purple-300 mb-2">🚀 Déploiement</h4>
                                        <FormattedText text={combo.deployment} />
                                    </div>
                                </div>
                                
                                {selectedCombination === index && (
                                    <div className="mt-6 pt-4 border-t border-gray-600">
                                        <h4 className="font-semibold text-yellow-300 mb-3">✨ Synergies & Avantages</h4>
                                        <p className="text-gray-300 leading-relaxed">
                                            <FormattedText text={combo.synergies} />
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Footer */}
            <div className="text-center pt-8 border-t border-gray-700">
                <p className="text-gray-400 text-sm">
                    💡 <strong>Conseil :</strong> Choisissez votre stack en fonction de vos besoins spécifiques, 
                    de votre équipe et de vos contraintes de temps et budget.
                </p>
            </div>
        </div>
    );
};

export default StacksTechnologiques;
