# **00_PROMPT-INITIAL.MD - BRIEFING INITIAL DE LA MISSION**

## **OBJECTIF PRINCIPAL DE LA PHASE DE BRIEFING**

Cette phase constitue le fondement de tout projet de développement. L'agent de développement IA doit collecter, analyser et structurer toutes les informations nécessaires avant de passer aux phases techniques.

**Mission de l'agent :** Transformer une demande initiale en spécifications techniques détaillées et actionnables, en respectant scrupuleusement la méthodologie définie dans ce framework.

---

## **1. PROCESSUS DE COLLECTE D'INFORMATIONS**

### **1.1 Questionnaire Initial Obligatoire**

**RÈGLE ABSOLUE :** Avant toute action technique, l'agent DOIT poser ces questions à Cisco :

**Questions sur le Projet :**
1. **Quel est l'objectif principal de cette application ?**
2. **Qui sont les utilisateurs cibles ?** (profils, besoins, contraintes)
3. **Quelles sont les fonctionnalités essentielles ?** (MVP vs fonctionnalités avancées)
4. **Y a-t-il des contraintes techniques spécifiques ?** (performance, compatibilité, etc.)
5. **Quel est le délai de livraison souhaité ?**

**Questions sur le Design et UX :**
6. **Avez-vous des références visuelles ou maquettes ?**
7. **Quelle est l'identité visuelle souhaitée ?** (couleurs, typographie, style)
8. **L'application doit-elle être responsive ?** (mobile-first, desktop-first)
9. **Y a-t-il des exigences d'accessibilité particulières ?**

**Questions sur les Données et Backend :**
10. **Quels types de données l'application va-t-elle gérer ?**
11. **Y a-t-il des intégrations externes nécessaires ?** (APIs, services tiers)
12. **Quels sont les besoins d'authentification ?** (simple, OAuth, rôles)
13. **Quelles sont les exigences de sécurité ?**

### **1.2 Analyse des Réponses**

**Processus obligatoire :**
1. **Documenter** toutes les réponses dans le fichier `01_cahier-des-charges.md`
2. **Identifier** les zones d'ambiguïté nécessitant des clarifications
3. **Prioriser** les fonctionnalités selon leur importance
4. **Évaluer** la faisabilité technique avec la stack autorisée

---

## **2. VALIDATION ET APPROBATION**

### **2.1 Présentation du Plan d'Action**

**Avant de passer à la phase suivante, l'agent DOIT :**
1. **Résumer** sa compréhension du projet
2. **Présenter** l'architecture technique envisagée
3. **Lister** les composants et fonctionnalités à développer
4. **Estimer** la complexité et les risques potentiels
5. **Attendre** l'approbation explicite de Cisco

### **2.2 Critères de Validation**

**Le briefing est considéré comme complet uniquement si :**
- ✅ Toutes les questions obligatoires ont été posées et répondues
- ✅ Les objectifs du projet sont clairement définis
- ✅ La faisabilité technique est confirmée
- ✅ Les priorités de développement sont établies
- ✅ Cisco a validé explicitement le plan d'action

---

## **3. LIVRABLES DE CETTE PHASE**

### **3.1 Documents à Compléter**

**Obligatoire :**
1. **`01_cahier-des-charges.md`** : Spécifications techniques détaillées
2. **`02_user-stories.md`** : Scénarios utilisateur et fonctionnalités
3. **`03_maquettes-et-inspiration.md`** : Références visuelles et design
4. **`04_analyse-technique.md`** : Évaluation de faisabilité et architecture

### **3.2 Critères de Qualité**

**Chaque document doit être :**
- **Précis** : Aucune ambiguïté dans les spécifications
- **Complet** : Tous les aspects du projet couverts
- **Actionnable** : Informations directement utilisables pour le développement
- **Validé** : Approuvé explicitement par Cisco

---

## **4. TRANSITION VERS LA PHASE SUIVANTE**

### **4.1 Conditions de Passage**

**L'agent peut passer à la phase `01_ARCHITECTURE_ET_SETUP` uniquement si :**
- ✅ Tous les documents de briefing sont complétés
- ✅ Cisco a validé l'ensemble des spécifications
- ✅ Aucune zone d'ombre ne subsiste sur les exigences
- ✅ La stack technique est confirmée et appropriée

### **4.2 Checklist de Fin de Phase**

**Validation finale obligatoire :**
- [ ] Objectifs du projet clairement définis
- [ ] Utilisateurs cibles identifiés et analysés
- [ ] Fonctionnalités prioritaires listées et validées
- [ ] Contraintes techniques documentées
- [ ] Design et UX spécifiés
- [ ] Architecture technique approuvée
- [ ] Planning et priorités établis
- [ ] Approbation explicite de Cisco obtenue

---

**⚠️ RAPPEL CRITIQUE :** Cette phase de briefing est FONDAMENTALE. Toute précipitation ou négligence dans la collecte d'informations se traduira par des erreurs coûteuses dans les phases suivantes. L'agent DOIT prendre le temps nécessaire pour bien comprendre et documenter tous les aspects du projet.
