import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';

// Navigation links - Menu gauche et droit selon le design de référence
const leftNavLinks = [
    { name: 'Accueil', path: '/' },
    { name: 'À Propos', path: '/a-propos' },
    { name: 'Compétences', path: '/competences' },
];

const rightNavLinks = [
    { name: 'Portfolio', path: '/portfolio' },
    { name: 'Apps', path: '/apps' },
    { name: 'Assistance', path: '/assistance' },
    { name: 'Contact', path: '/contact' },
];

const Header: React.FC = () => {
    const [isScrolled, setIsScrolled] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 50);
        };
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    // Style pour les liens actifs avec gradient Gemini
    const activeLinkStyle = {
        background: 'linear-gradient(90deg, #1A3452 0%, #2190F6 25%, #6689EF 50%, #8D86ED 75%, #AE87F3 100%)',
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: 'none'
    };

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    return (
        <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
            isScrolled
                ? 'bg-black/95 backdrop-blur-lg border-b border-slate-700 py-2'
                : 'bg-transparent py-4'
        }`}>
            <div className="container mx-auto px-4 relative">
                {/* Logo Central */}
                <div className={`absolute left-1/2 transform -translate-x-1/2 transition-all duration-300 ${
                    isScrolled
                        ? 'top-1 scale-75'
                        : 'top-0 scale-100'
                }`}>
                    <NavLink to="/" className="block">
                        <img
                            src="/assets/02-Logo-FlexoDiv.png"
                            alt="FlexoDiv Logo"
                            className={`object-contain hover:scale-105 transition-all duration-300 drop-shadow-2xl ${
                                isScrolled ? 'w-16 h-16' : 'w-32 h-32'
                            }`}
                        />
                    </NavLink>
                </div>

                <div className={`flex items-center justify-between ${
                    isScrolled ? 'h-16' : 'h-20'
                }`}>
                    {/* Menu Gauche */}
                    <div className="hidden md:flex space-x-6">
                        {leftNavLinks.map((link) => (
                            <NavLink
                                key={link.name}
                                to={link.path}
                                className="text-slate-300 hover:text-white font-medium transition-all duration-300 relative"
                                style={({ isActive }) => (isActive ? activeLinkStyle : {})}
                            >
                                {link.name}
                            </NavLink>
                        ))}
                    </div>

                    {/* Espace central pour le logo */}
                    <div className={`${isScrolled ? 'w-16' : 'w-32'} transition-all duration-300`}></div>

                    {/* Menu Droit */}
                    <div className="hidden md:flex space-x-6">
                        {rightNavLinks.map((link) => (
                            <NavLink
                                key={link.name}
                                to={link.path}
                                className="text-slate-300 hover:text-white font-medium transition-all duration-300 relative"
                                style={({ isActive }) => (isActive ? activeLinkStyle : {})}
                            >
                                {link.name}
                            </NavLink>
                        ))}
                    </div>

                    {/* Menu mobile */}
                    <div className="md:hidden">
                        <button
                            onClick={toggleMobileMenu}
                            className="text-white p-2 hover:text-slate-300 transition-colors"
                            aria-label="Menu mobile"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d={isMobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                                />
                            </svg>
                        </button>
                    </div>
                </div>

                {/* Menu mobile dropdown */}
                {isMobileMenuOpen && (
                    <div className="md:hidden absolute top-full left-0 right-0 bg-black/95 backdrop-blur-lg border-t border-slate-700">
                        <div className="px-4 py-4 space-y-2">
                            {[...leftNavLinks, ...rightNavLinks].map((link) => (
                                <NavLink
                                    key={link.name}
                                    to={link.path}
                                    className="block text-slate-300 hover:text-white font-medium py-2 transition-colors"
                                    style={({ isActive }) => (isActive ? activeLinkStyle : {})}
                                    onClick={() => setIsMobileMenuOpen(false)}
                                >
                                    {link.name}
                                </NavLink>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </nav>
    );
};

export default Header;