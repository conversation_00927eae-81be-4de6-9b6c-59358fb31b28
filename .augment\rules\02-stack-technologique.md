---
type: always
description: "Stack technologique autorisée - Technologies obligatoires et interdites"
---

# STACK TECHNOLOGIQUE AUTORISÉE

## TECHNOLOGIES OBLIGATOIRES

### Framework & Langage
- **React 18+** : Base de toute l'application, composants fonctionnels uniquement
- **TypeScript** : Typage rigoureux obligatoire, interdiction formelle de `any`

**Rôle :** Base de toute l'application. Construire l'interface utilisateur sous forme de composants fonctionnels.

### Environnement de Développement
- **Vite.js** : Serveur de développement et bundler de production
- **Rechargement à chaud (HMR)** : Expérience de développement optimisée

**Rôle :** Servir de serveur de développement local et de bundler pour la production.
**Objectif :** Bénéficier d'une expérience de développement rapide avec rechargement à chaud (HMR).

### Styling & UI
- **Tailwind CSS** : Gestion exclusive du style via classes utilitaires
- **shadcn/ui** : Base pour les composants d'interface (boutons, cartes, modales)

**Rôle :** Gérer tout le style de l'application via des classes utilitaires.

### Animations & 3D
- **GSAP (GreenSock Animation Platform)** : Animations 2D complexes et séquencées
- **Three.js** avec **@react-three/fiber** et **@react-three/drei** : Scènes 3D

**Rôle :** 
- GSAP pour toutes les animations complexes et séquencées
- Three.js avec les helpers @react-three/fiber et @react-three/drei pour les scènes 3D

### Déploiement
- **Netlify** : Hébergement en production et gestion des formulaires

**Rôle :** Héberger le site en production.
**Objectif :** Assurer un déploiement simple et automatisé depuis le dépôt Git. Gérer les formulaires via "Netlify Forms".

## TECHNOLOGIES INTERDITES

Toute technologie non listée dans la section précédente est **FORMELLEMENT INTERDITE** sans validation explicite de Cisco.

## VARIABLES D'ENVIRONNEMENT (SÉCURITÉ OBLIGATOIRE)

### Configuration .env.local
- **OBLIGATION** de créer `.env.local` à la racine pour TOUTES les clés d'API
- Préfixage Vite obligatoire : `VITE_` pour les variables client
- Exemple : `VITE_FIREBASE_API_KEY=AIza...`
- **IMPÉRATIF** : Ajouter `.env.local` au `.gitignore`

### Template .env.local obligatoire
```env
# Configuration Firebase
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef

# Variables serveur (sans préfixe VITE_)
FIREBASE_ADMIN_SDK_KEY=server_only_key
```

## GESTION DES DÉPENDANCES

**TOUJOURS utiliser les gestionnaires de packages appropriés :**
- **JavaScript/Node.js** : `npm install`, `npm uninstall`, `yarn add`, `yarn remove`, ou `pnpm add/remove`
- **JAMAIS** éditer manuellement package.json sans utiliser les commandes appropriées

**Rationale :** Les gestionnaires de packages résolvent automatiquement les versions correctes, gèrent les conflits de dépendances, mettent à jour les fichiers de verrouillage, et maintiennent la cohérence entre les environnements.
