/* Effet néon laser pour les cartes - Copie exacte du code qui fonctionnait */

@property --angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

:root {
  --gemini-blue-light: #2190F6;
  --gemini-blue-purple: #6689EF;
  --gemini-purple: rgb(77, 70, 175);
  --gemini-salmon: rgb(235, 73, 114);
}

/* Classe utilitaire pour l'effet néon laser - EXACTEMENT comme glowing-card */
.glowing-effect {
  position: relative;
}

.glowing-effect::before,
.glowing-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.glowing-effect::before,
.glowing-effect::after {
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
}

.glowing-effect::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
}

.glowing-effect::after {
  filter: brightness(2.5);
}

.glowing-effect::before,
.glowing-effect::after {
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}

.glowing-effect:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}

.glowing-effect:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}

@keyframes spin {
  from { --angle: 0deg; }
  to { --angle: 360deg; }
}

/* L'effet reste en arrière-plan grâce au z-index: -1 des pseudo-éléments */

/* Classe générale combinée pour faciliter l'usage */
.neon-card {
  position: relative;
  background-color: #1A2035; /* bg-brand-surface */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 2rem; /* p-8 */
  border: 1px solid #1A2035; /* border border-brand-surface */
}

.neon-card::before,
.neon-card::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}

.neon-card::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
}

.neon-card::after {
  filter: brightness(2.5);
}

.neon-card:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}

.neon-card:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}

/* Variante petite pour les cartes plus compactes */
.neon-card-sm {
  position: relative;
  background-color: #1A2035; /* bg-brand-surface */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 1.5rem; /* p-6 */
  border: 1px solid #1A2035; /* border border-brand-surface */
}

.neon-card-sm::before,
.neon-card-sm::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}

.neon-card-sm::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
}

.neon-card-sm::after {
  filter: brightness(2.5);
}

.neon-card-sm:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}

.neon-card-sm:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}

/* Classe spéciale pour les cartes de portfolio avec images - Effet de bordure uniquement */
.neon-card-portfolio {
  position: relative;
  background-color: #1A2035; /* bg-brand-surface */
  border-radius: 0.5rem; /* rounded-lg */
  border: 2px solid transparent; /* Bordure transparente pour l'effet */
  overflow: hidden;
  background-clip: padding-box; /* Important pour l'effet de bordure */
}

.neon-card-portfolio::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  z-index: -1;
  border-radius: inherit;
  background: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
  pointer-events: none;
}

.neon-card-portfolio:hover::before {
  opacity: 1;
  animation: spin 5s linear infinite;
}

/* Pas de pseudo-élément ::after pour éviter les conflits */
/* Le contenu reste naturellement au-dessus */
