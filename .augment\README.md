# RÈGLES AUGMENT - FRAMEWORK D'INSTRUCTIONS

## Vue d'ensemble

Ce dossier contient les règles Augment modernes, extraites et optimisées à partir du framework d'instructions générales. Chaque règle est spécialisée et configurée selon les besoins du projet.

## Organisation des Règles

### 📋 Règles "Always" (Toujours Actives)
Ces règles sont automatiquement appliquées à chaque conversation :

- **01-regles-fondamentales.md** : Principes inviolables, règle d'or, communication français
- **02-stack-technologique.md** : Technologies autorisées/interdites, configuration environnement
- **06-accessibilite-securite.md** : Standards WCAG, sécurité frontend/backend, RGPD
- **07-workflow-validation.md** : Processus de développement, critères de validation

### 🤖 Règles "Auto" (Détection Automatique)
Ces règles se déclenchent automatiquement selon le contexte :

- **03-standards-react-typescript.md** : Se déclenche quand React/TypeScript détecté
- **05-performance-optimisation.md** : Se déclenche quand des problèmes de performance sont détectés

### 🎯 Règles "Manual" (Activation Manuelle)
Ces règles doivent être activées manuellement via @ mention :

- **04-firebase-integration.md** : À activer quand Firebase est utilisé dans le projet

## Comment Utiliser les Règles

### Activation Automatique
Les règles "Always" et "Auto" s'activent automatiquement selon leur configuration.

### Activation Manuelle
Pour activer une règle "Manual", utilisez @ mention dans Augment Chat :
```
@04-firebase-integration.md Comment configurer Firestore ?
```

### Modification des Règles
1. Éditez directement les fichiers .md dans ce dossier
2. Les modifications sont prises en compte immédiatement
3. Respectez le format YAML front-matter en en-tête

## Structure d'une Règle

```markdown
---
type: always|auto|manual
description: "Description de la règle"
---

# TITRE DE LA RÈGLE

Contenu de la règle en markdown...
```

## Avantages de cette Organisation

### ✅ Modularité
- Chaque domaine a sa règle spécialisée
- Facilite la maintenance et les mises à jour
- Évite la redondance d'informations

### ✅ Performance
- Seules les règles pertinentes sont chargées
- Réduction de la charge cognitive pour l'IA
- Réponses plus précises et contextuelles

### ✅ Flexibilité
- Activation/désactivation granulaire
- Adaptation selon le type de projet
- Évolution indépendante de chaque règle

## Relation avec AGENT_INSTRUCTIONS_GENERALES.md

Le fichier `AGENT_INSTRUCTIONS_GENERALES.md` reste la **référence complète** du framework. Les règles Augment sont des **extraits optimisés** pour l'utilisation avec Augment Agent et Chat.

### Utilisation Recommandée
- **Augment Agent/Chat** : Utiliser les règles de ce dossier
- **Documentation complète** : Consulter `AGENT_INSTRUCTIONS_GENERALES.md`
- **Formation d'équipe** : S'appuyer sur le document maître

## Maintenance

### Synchronisation
Lors de mises à jour du framework principal :
1. Identifier les sections modifiées
2. Mettre à jour les règles correspondantes
3. Tester l'activation des règles
4. Documenter les changements

### Versioning
- Les règles suivent la version du framework principal
- Chaque modification majeure est documentée
- Compatibilité maintenue avec les versions précédentes

---

**Version actuelle :** 5.0  
**Dernière synchronisation :** 30/07/2025  
**Basé sur :** AGENT_INSTRUCTIONS_GENERALES.md v5.0
