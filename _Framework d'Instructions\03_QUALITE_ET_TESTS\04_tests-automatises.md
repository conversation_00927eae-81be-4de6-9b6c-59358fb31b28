# **04_TESTS-AUTOMATISES.MD - STRATÉGIE DE TESTS AUTOMATISÉS**

## **OBJECTIF DES TESTS AUTOMATISÉS**

Ce document définit la stratégie complète de tests automatisés pour garantir la qualité, la fiabilité et la maintenabilité du code. Les tests automatisés sont obligatoires et constituent un prérequis pour tout déploiement.

---

## **1. PYRAMIDE DE TESTS**

### **1.1 Architecture des Tests**

**Répartition obligatoire :**
- **Tests unitaires (70%)** : Fonctions, hooks, utilitaires
- **Tests d'intégration (20%)** : Composants avec dépendances
- **Tests end-to-end (10%)** : Parcours utilisateur complets

### **1.2 Outils et Framework**

**Stack de test obligatoire :**
- **Vitest** : Runner de tests et assertions
- **Testing Library** : Tests de composants React
- **MSW** : Mock des appels API
- **Playwright** : Tests end-to-end (si nécessaire)

---

## **2. CONFIGURATION DES TESTS**

### **2.1 Configuration Vitest**

**Fichier `vitest.config.ts` :**
```typescript
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/index.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@services': path.resolve(__dirname, './src/services'),
      '@types': path.resolve(__dirname, './src/types')
    }
  }
})
```

### **2.2 Setup des Tests**

**Fichier `src/test/setup.ts` :**
```typescript
import '@testing-library/jest-dom'
import { cleanup } from '@testing-library/react'
import { afterEach, beforeAll, afterAll } from 'vitest'
import { server } from './mocks/server'

// Configuration globale
beforeAll(() => {
  // Démarrer le serveur MSW
  server.listen({ onUnhandledRequest: 'error' })
})

afterEach(() => {
  // Nettoyer après chaque test
  cleanup()
  server.resetHandlers()
})

afterAll(() => {
  // Arrêter le serveur MSW
  server.close()
})

// Mock des APIs du navigateur
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock de ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock de IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))
```

---

## **3. TESTS UNITAIRES**

### **3.1 Tests de Fonctions Utilitaires**

**Exemple de test unitaire :**
```typescript
// utils/formatters.test.ts
import { describe, it, expect } from 'vitest'
import { formatCurrency, formatDate, truncateText } from './formatters'

describe('formatters', () => {
  describe('formatCurrency', () => {
    it('should format positive numbers correctly', () => {
      expect(formatCurrency(1234.56)).toBe('1 234,56 €')
      expect(formatCurrency(0)).toBe('0,00 €')
    })

    it('should handle negative numbers', () => {
      expect(formatCurrency(-1234.56)).toBe('-1 234,56 €')
    })

    it('should handle edge cases', () => {
      expect(formatCurrency(NaN)).toBe('0,00 €')
      expect(formatCurrency(Infinity)).toBe('0,00 €')
    })
  })

  describe('formatDate', () => {
    it('should format dates correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z')
      expect(formatDate(date)).toBe('15/01/2024')
    })

    it('should handle invalid dates', () => {
      expect(formatDate(new Date('invalid'))).toBe('Date invalide')
    })
  })

  describe('truncateText', () => {
    it('should truncate long text', () => {
      const longText = 'Lorem ipsum dolor sit amet consectetur'
      expect(truncateText(longText, 20)).toBe('Lorem ipsum dolor...')
    })

    it('should not truncate short text', () => {
      const shortText = 'Hello'
      expect(truncateText(shortText, 20)).toBe('Hello')
    })
  })
})
```

### **3.2 Tests de Hooks Personnalisés**

**Test d'un hook personnalisé :**
```typescript
// hooks/useLocalStorage.test.ts
import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, beforeEach } from 'vitest'
import { useLocalStorage } from './useLocalStorage'

describe('useLocalStorage', () => {
  beforeEach(() => {
    localStorage.clear()
  })

  it('should return initial value when no stored value exists', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    expect(result.current[0]).toBe('initial')
  })

  it('should return stored value when it exists', () => {
    localStorage.setItem('test-key', JSON.stringify('stored'))
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    expect(result.current[0]).toBe('stored')
  })

  it('should update stored value when setValue is called', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    
    act(() => {
      result.current[1]('updated')
    })

    expect(result.current[0]).toBe('updated')
    expect(localStorage.getItem('test-key')).toBe('"updated"')
  })

  it('should handle localStorage errors gracefully', () => {
    // Mock localStorage.setItem to throw an error
    const originalSetItem = localStorage.setItem
    localStorage.setItem = vi.fn(() => {
      throw new Error('Storage full')
    })

    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'))
    
    act(() => {
      result.current[1]('new-value')
    })

    // Should not crash and should keep the value in state
    expect(result.current[0]).toBe('new-value')

    // Restore original method
    localStorage.setItem = originalSetItem
  })
})
```

---

## **4. TESTS DE COMPOSANTS**

### **4.1 Tests de Composants de Présentation**

**Test d'un composant simple :**
```typescript
// components/ui/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { Button } from './Button'

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledOnce()
  })

  it('applies correct variant classes', () => {
    render(<Button variant="destructive">Delete</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-destructive')
  })

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled</Button>)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
  })

  it('shows loading state correctly', () => {
    render(<Button loading>Loading</Button>)
    expect(screen.getByRole('button')).toBeDisabled()
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })
})
```

### **4.2 Tests de Composants avec État**

**Test d'un composant avec logique :**
```typescript
// components/features/Counter.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { Counter } from './Counter'

describe('Counter', () => {
  it('renders with initial value', () => {
    render(<Counter initialValue={5} />)
    expect(screen.getByText('Count: 5')).toBeInTheDocument()
  })

  it('increments count when increment button is clicked', () => {
    render(<Counter initialValue={0} />)
    
    fireEvent.click(screen.getByRole('button', { name: 'Increment' }))
    expect(screen.getByText('Count: 1')).toBeInTheDocument()
  })

  it('decrements count when decrement button is clicked', () => {
    render(<Counter initialValue={5} />)
    
    fireEvent.click(screen.getByRole('button', { name: 'Decrement' }))
    expect(screen.getByText('Count: 4')).toBeInTheDocument()
  })

  it('resets count when reset button is clicked', () => {
    render(<Counter initialValue={10} />)
    
    // Increment first
    fireEvent.click(screen.getByRole('button', { name: 'Increment' }))
    expect(screen.getByText('Count: 11')).toBeInTheDocument()
    
    // Then reset
    fireEvent.click(screen.getByRole('button', { name: 'Reset' }))
    expect(screen.getByText('Count: 10')).toBeInTheDocument()
  })

  it('calls onCountChange when count changes', () => {
    const onCountChange = vi.fn()
    render(<Counter initialValue={0} onCountChange={onCountChange} />)
    
    fireEvent.click(screen.getByRole('button', { name: 'Increment' }))
    expect(onCountChange).toHaveBeenCalledWith(1)
  })
})
```

---

## **5. TESTS D'INTÉGRATION**

### **5.1 Configuration MSW pour les Mocks API**

**Fichier `src/test/mocks/handlers.ts` :**
```typescript
import { rest } from 'msw'

export const handlers = [
  // Mock des endpoints utilisateur
  rest.get('/api/users/:id', (req, res, ctx) => {
    const { id } = req.params
    
    return res(
      ctx.json({
        id,
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg'
      })
    )
  }),

  rest.post('/api/users', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        id: '123',
        name: 'New User',
        email: '<EMAIL>'
      })
    )
  }),

  // Mock d'erreur pour tester la gestion d'erreurs
  rest.get('/api/error', (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({
        message: 'Internal server error'
      })
    )
  }),

  // Mock avec délai pour tester les états de chargement
  rest.get('/api/slow', (req, res, ctx) => {
    return res(
      ctx.delay(2000),
      ctx.json({ data: 'slow response' })
    )
  })
]
```

**Fichier `src/test/mocks/server.ts` :**
```typescript
import { setupServer } from 'msw/node'
import { handlers } from './handlers'

export const server = setupServer(...handlers)
```

### **5.2 Tests d'Intégration avec API**

**Test d'un composant avec appels API :**
```typescript
// components/features/UserProfile.test.tsx
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { UserProfile } from './UserProfile'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Wrapper pour React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('UserProfile Integration', () => {
  it('loads and displays user data', async () => {
    render(<UserProfile userId="123" />, { wrapper: createWrapper() })
    
    // Vérifier l'état de chargement
    expect(screen.getByText('Chargement...')).toBeInTheDocument()
    
    // Attendre que les données se chargent
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })
    
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('handles API errors gracefully', async () => {
    // Override le handler pour retourner une erreur
    server.use(
      rest.get('/api/users/:id', (req, res, ctx) => {
        return res(ctx.status(404), ctx.json({ message: 'User not found' }))
      })
    )

    render(<UserProfile userId="999" />, { wrapper: createWrapper() })
    
    await waitFor(() => {
      expect(screen.getByText('Utilisateur non trouvé')).toBeInTheDocument()
    })
  })
})
```

---

## **6. TESTS DE FORMULAIRES**

### **6.1 Tests de Validation**

**Test d'un formulaire avec validation :**
```typescript
// components/forms/LoginForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'
import { LoginForm } from './LoginForm'

describe('LoginForm', () => {
  it('validates required fields', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={vi.fn()} />)
    
    // Essayer de soumettre sans remplir les champs
    await user.click(screen.getByRole('button', { name: 'Se connecter' }))
    
    await waitFor(() => {
      expect(screen.getByText('L\'email est requis')).toBeInTheDocument()
      expect(screen.getByText('Le mot de passe est requis')).toBeInTheDocument()
    })
  })

  it('validates email format', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={vi.fn()} />)
    
    await user.type(screen.getByLabelText('Email'), 'invalid-email')
    await user.click(screen.getByRole('button', { name: 'Se connecter' }))
    
    await waitFor(() => {
      expect(screen.getByText('Format d\'email invalide')).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    render(<LoginForm onSubmit={onSubmit} />)
    
    await user.type(screen.getByLabelText('Email'), '<EMAIL>')
    await user.type(screen.getByLabelText('Mot de passe'), 'password123')
    await user.click(screen.getByRole('button', { name: 'Se connecter' }))
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })
  })

  it('shows loading state during submission', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn(() => new Promise(resolve => setTimeout(resolve, 1000)))
    render(<LoginForm onSubmit={onSubmit} />)
    
    await user.type(screen.getByLabelText('Email'), '<EMAIL>')
    await user.type(screen.getByLabelText('Mot de passe'), 'password123')
    await user.click(screen.getByRole('button', { name: 'Se connecter' }))
    
    expect(screen.getByRole('button', { name: 'Connexion...' })).toBeDisabled()
  })
})
```

---

## **7. TESTS DE PERFORMANCE**

### **7.1 Tests de Rendu**

**Test de performance des composants :**
```typescript
// components/performance/LargeList.test.tsx
import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { LargeList } from './LargeList'

describe('LargeList Performance', () => {
  it('renders large list efficiently', () => {
    const items = Array.from({ length: 10000 }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      description: `Description for item ${i}`
    }))

    const startTime = performance.now()
    render(<LargeList items={items} />)
    const endTime = performance.now()

    // Le rendu ne devrait pas prendre plus de 100ms
    expect(endTime - startTime).toBeLessThan(100)
    
    // Vérifier que la virtualisation fonctionne
    // (seulement les éléments visibles sont rendus)
    const renderedItems = screen.getAllByTestId(/list-item-/)
    expect(renderedItems.length).toBeLessThan(50) // Moins que le total
  })

  it('handles scroll performance', async () => {
    const items = Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` }))
    render(<LargeList items={items} />)

    const listContainer = screen.getByTestId('list-container')
    
    const startTime = performance.now()
    
    // Simuler plusieurs événements de scroll
    for (let i = 0; i < 10; i++) {
      fireEvent.scroll(listContainer, { target: { scrollTop: i * 100 } })
    }
    
    const endTime = performance.now()
    
    // Le scroll ne devrait pas être bloquant
    expect(endTime - startTime).toBeLessThan(50)
  })
})
```

---

## **8. COUVERTURE DE CODE**

### **8.1 Objectifs de Couverture**

**Seuils obligatoires :**
- **Branches** : 80% minimum
- **Fonctions** : 80% minimum
- **Lignes** : 80% minimum
- **Statements** : 80% minimum

### **8.2 Exclusions de Couverture**

**Fichiers exclus de la couverture :**
- Fichiers de configuration (`*.config.*`)
- Fichiers de types (`*.d.ts`)
- Fichiers de test (`*.test.*`, `*.spec.*`)
- Fichiers d'index (`index.ts` d'export uniquement)
- Mocks et stubs de test

### **8.3 Rapport de Couverture**

**Commandes de génération :**
```bash
# Exécuter les tests avec couverture
npm run test:coverage

# Générer un rapport HTML
npm run test:coverage -- --reporter=html

# Vérifier les seuils
npm run test:coverage -- --coverage.thresholds.global.branches=80
```

---

## **9. INTÉGRATION CONTINUE**

### **9.1 Pipeline de Tests**

**Étapes obligatoires dans le CI :**
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test:coverage
      - run: npm run build
      
      # Upload coverage to codecov
      - uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

### **9.2 Quality Gates**

**Critères de blocage :**
- Tests en échec : ❌ Bloque le merge
- Couverture < 80% : ❌ Bloque le merge
- Erreurs ESLint : ❌ Bloque le merge
- Erreurs TypeScript : ❌ Bloque le merge
- Build en échec : ❌ Bloque le merge

---

## **10. BONNES PRATIQUES**

### **10.1 Règles de Nommage**

**Conventions obligatoires :**
- Fichiers de test : `ComponentName.test.tsx`
- Describe blocks : Description du composant/fonction
- Test cases : Comportement attendu en français
- Variables de test : Noms explicites

### **10.2 Structure des Tests**

**Pattern AAA obligatoire :**
```typescript
it('should do something when condition is met', () => {
  // Arrange - Préparer les données et mocks
  const mockData = { id: 1, name: 'Test' }
  const mockCallback = vi.fn()
  
  // Act - Exécuter l'action à tester
  render(<Component data={mockData} onAction={mockCallback} />)
  fireEvent.click(screen.getByRole('button'))
  
  // Assert - Vérifier le résultat
  expect(mockCallback).toHaveBeenCalledWith(mockData.id)
})
```

### **10.3 Maintenance des Tests**

**Règles de maintenance :**
- Tests mis à jour avec le code
- Suppression des tests obsolètes
- Refactoring des tests dupliqués
- Documentation des tests complexes

---

**⚠️ IMPORTANT :** Les tests automatisés sont obligatoires pour tous les composants, hooks et fonctions utilitaires. Aucun code ne peut être mergé sans tests appropriés et une couverture suffisante.
