import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { CheckCircle, Star, Wrench, RotateCcw, Zap, Users, Clock, Shield, ExternalLink, Phone, Mail, MapPin, Headphones } from 'lucide-react';
import '../css/glowing-effect.css';

interface AssistancePackage {
    id: string;
    title: string;
    price: string;
    description: string;
    features: string[];
    icon: React.ReactNode;
    paypalLink: string;
    popular?: boolean;
}

const assistancePackages: AssistancePackage[] = [
    {
        id: 'diagnostic',
        title: 'Diagnostic',
        price: '9,99€',
        description: 'Solution express pour une correction unique, rapide et ciblée de votre site web.',
        features: [
            'Correction CSS ciblée',
            'Mise à jour de contenu',
            'Réparation d\'un élément spécifique',
            'Intervention rapide'
        ],
        icon: <CheckCircle className="w-16 h-16" />,
        paypalLink: 'https://www.paypal.com/ncp/payment/XTMX8UVUXL9C4'
    },
    {
        id: 'reparation',
        title: 'Réparation',
        price: '24,99€',
        description: 'Solution ciblée pour réparer plusieurs problèmes liés sur votre site web.',
        features: [
            'Réparation Responsive',
            'Correction de bugs JavaScript',
            'Optimisation des performances',
            'Tests multi-navigateurs'
        ],
        icon: <Wrench className="w-16 h-16" />,
        paypalLink: 'https://www.paypal.com/ncp/payment/Y47VVWDF8XPKJ',
        popular: true
    },
    {
        id: 'restauration',
        title: 'Restauration',
        price: '49,99€',
        description: 'Solution complète pour les pannes complexes qui combinent plusieurs bugs ou touchent à la logique de votre site.',
        features: [
            'Restauration Complète',
            'Reconstruction d\'une page complète',
            'Mise à jour des technologies',
            'Vérification de sécurité'
        ],
        icon: <RotateCcw className="w-16 h-16" />,
        paypalLink: 'https://www.paypal.com/ncp/payment/WWUY2CZ88Y4K6'
    },
    {
        id: 'complete',
        title: 'Intervention Complète',
        price: '99,99€',
        description: 'Solution premium pour une refonte complète, une optimisation avancée ou un projet d\'envergure.',
        features: [
            'Refonte Complète',
            'Optimisation Avancée',
            'Fonctionnalités Avancées',
            'Support 30 jours inclus'
        ],
        icon: <Zap className="w-16 h-16" />,
        paypalLink: 'https://www.paypal.com/ncp/payment/GQHFAZ83BGMK4'
    },
    {
        id: 'custom',
        title: 'Intervention Sur Devis',
        price: 'Sur Devis',
        description: 'Solution personnalisée pour vos projets complexes et spécifiques qui nécessitent une approche sur-mesure.',
        features: [
            'Analyse complète',
            'Devis personnalisé gratuit',
            'Solution sur mesure',
            'Accompagnement dédié'
        ],
        icon: <Star className="w-16 h-16" />,
        paypalLink: 'https://www.paypal.com/ncp/payment/ZGJHPR2TU2A8G'
    },
    {
        id: 'availability',
        title: 'Disponibilité Pro',
        price: 'Informations',
        description: 'Intervention à distance 24h/7j. Fournissez-moi vos identifiants pour une assistance immédiate et professionnelle.',
        features: [
            'Disponible 24h/24, 7j/7',
            'Intervention 100% à distance',
            'Accès sécurisé requis',
            'Réactivité garantie'
        ],
        icon: <Headphones className="w-16 h-16" />,
        paypalLink: '#contact'
    }
];

interface Partner {
    name: string;
    logo: string;
    category: string;
    description: string;
    website?: string;
    phone?: string;
    email?: string;
    locations?: string[];
}

const partners: Partner[] = [
    // Agences d'Intérim Généralistes avec branche IT
    {
        name: 'Randstad Digital',
        logo: '/assets/agences-interim/Randstad Digital.png',
        category: 'Intérim Généraliste IT',
        description: 'Spécialiste du recrutement digital et IT avec présence locale dans l\'Allier',
        website: 'https://www.randstad.fr/',
        phone: '+33 1 49 17 85 85',
        locations: ['Montluçon', 'Moulins']
    },
    {
        name: 'Modis (Groupe Adecco)',
        logo: '/assets/agences-interim/Adecco-Modis.webp',
        category: 'Intérim Généraliste IT',
        description: 'Division IT du groupe Adecco, expert en solutions technologiques',
        website: 'https://www.adecco.fr/',
        phone: '+33 4 72 82 72 82',
        locations: ['Montluçon', 'Vichy', 'Moulins']
    },
    {
        name: 'Manpower IT',
        logo: '/assets/agences-interim/Manpower.png',
        category: 'Intérim Généraliste IT',
        description: 'Services IT spécialisés du leader mondial de l\'intérim',
        website: 'https://vosagences.manpower.fr/',
        phone: '+33 1 57 66 22 22',
        locations: ['Montluçon', 'Vichy', 'Moulins']
    },
    // Cabinets de Recrutement Spécialisés Tech
    {
        name: 'Hays Technology',
        logo: '/assets/agences-interim/hays-og-optimize-image-new.jpg',
        category: 'Cabinet Spécialisé Tech',
        description: 'Cabinet de recrutement international spécialisé en technologies',
        website: 'https://www.hays.fr/',
        phone: '+33 1 44 69 51 51'
    },
    {
        name: 'Urban Linker',
        logo: '/assets/agences-interim/Urban-Linker.jpg',
        category: 'Cabinet Spécialisé Tech',
        description: 'Cabinet de recrutement tech nouvelle génération',
        website: 'https://urbanlinker.com/',
        phone: '+33 1 88 33 58 80',
        email: '<EMAIL>'
    },
    {
        name: 'Externatic',
        logo: '/assets/agences-interim/logo-externatic.svg',
        category: 'Cabinet Spécialisé Tech',
        description: 'Cabinet de recrutement spécialisé développement web et mobile',
        website: 'https://www.externatic.fr/',
        phone: '+33 2 85 52 65 98',
        email: '<EMAIL>'
    },
    // Cabinets Ultra-Spécialisés
    {
        name: 'Seyos',
        logo: '/assets/agences-interim/seyos-logo.png',
        category: 'Ultra-Spécialisé Front-End',
        description: 'Cabinet ultra-spécialisé en développement front-end et UX/UI',
        website: 'https://www.seyos.fr/',
        phone: '+33 2 51 72 73 96',
        email: '<EMAIL>'
    },
    {
        name: 'Getpro.io',
        logo: '/assets/agences-interim/GetPro-agence.svg',
        category: 'Ultra-Spécialisé Front-End',
        description: 'Plateforme de recrutement tech en full remote',
        website: 'https://www.getpro.co/'
    },
    {
        name: 'Michael Page Technology',
        logo: '/assets/agences-interim/MichaelPage-agence.png',
        category: 'Cabinet Spécialisé Tech',
        description: 'Division technologie du cabinet de recrutement international',
        website: 'https://www.michaelpage.fr/jobs/technology',
        phone: '+33 1 41 99 84 00'
    }
];

const Assistance: React.FC = () => {
    return (
        <div className="container mx-auto px-6 py-20">
            {/* Hero Section */}
            <section className="text-center max-w-4xl mx-auto mb-20">
                <h1 className="text-5xl font-bold mb-6">Assistance Technique</h1>
                <p className="text-xl text-brand-muted leading-relaxed">
                    Support technique professionnel pour vos projets web et IA. Diagnostic, réparation,
                    optimisation et maintenance - je vous accompagne à chaque étape.
                </p>
            </section>

            {/* Section explicative des tarifs */}
            <section className="mb-20">
                <div className="neon-card max-w-5xl mx-auto">
                    <h2 className="text-3xl font-bold text-center mb-8">Une approche transparente et solidaire</h2>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        {/* Pour les particuliers */}
                        <div className="neon-card-sm">
                            <div className="flex items-center mb-4">
                                <Users className="w-8 h-8 text-brand-blue mr-3" />
                                <h3 className="text-xl font-semibold">Pour les Particuliers</h3>
                            </div>
                            <div className="space-y-4 text-brand-muted">
                                <p>
                                    <strong className="text-white">Tarifs transparents :</strong> Les prix affichés correspondent uniquement
                                    aux frais de l'assistant de codage IA que j'utilise pour vous aider.
                                </p>
                                <p>
                                    <strong className="text-white">Service gratuit :</strong> Je ne prends aucune marge sur ces interventions.
                                    Mon objectif est de vous rendre service et de me faire connaître avant de relancer officiellement mon entreprise.
                                </p>
                                <p>
                                    <strong className="text-white">Engagement qualité :</strong> Même si c'est gratuit pour moi,
                                    vous bénéficiez du même niveau de professionnalisme et de qualité.
                                </p>
                            </div>
                        </div>

                        {/* Pour les entreprises */}
                        <div className="neon-card-sm">
                            <div className="flex items-center mb-4">
                                <Shield className="w-8 h-8 text-brand-purple mr-3" />
                                <h3 className="text-xl font-semibold">Pour les Entreprises</h3>
                            </div>
                            <div className="space-y-4 text-brand-muted">
                                <p>
                                    <strong className="text-white">Intervention sur devis :</strong> Pour les projets d'entreprise,
                                    une évaluation personnalisée est nécessaire.
                                </p>
                                <p>
                                    <strong className="text-white">Processus professionnel :</strong> N'étant pas encore officiellement
                                    à mon compte, les entreprises devront passer par mon agence d'intérim partenaire.
                                </p>
                                <p>
                                    <strong className="text-white">Devis gratuit :</strong> L'analyse de vos besoins et
                                    l'établissement du devis restent entièrement gratuits.
                                </p>
                                <div className="mt-4">
                                    <button
                                        onClick={() => document.getElementById('partenaires-section')?.scrollIntoView({ behavior: 'smooth' })}
                                        className="inline-flex items-center bg-gradient-to-r from-brand-purple to-brand-blue text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg hover:shadow-brand-purple/25 transition-all duration-300 text-sm"
                                    >
                                        <Users className="w-4 h-4 mr-2" />
                                        Voir nos partenaires agences
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Message de motivation */}
                    <div className="text-center p-6 bg-gradient-to-r from-brand-blue/10 to-brand-purple/10 rounded-lg border border-brand-blue/20">
                        <h4 className="text-lg font-semibold mb-3 text-brand-blue">Pourquoi cette approche ?</h4>
                        <p className="text-brand-muted leading-relaxed">
                            Cette période de transition me permet de constituer un réseau de clients satisfaits
                            et de démontrer la qualité de mes services. Votre satisfaction et vos recommandations
                            sont ma meilleure récompense pour préparer le lancement officiel de mon entreprise.
                        </p>
                    </div>
                </div>
            </section>

            {/* Packs d'Assistance */}
            <section className="mb-20">
                <h2 className="text-3xl font-bold text-center mb-16">Packs d'Assistance</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                    {assistancePackages.map((pack) => (
                        <div
                            key={pack.id}
                            className="neon-card text-center flex flex-col h-full"
                        >
                            {pack.popular && (
                                <div className="bg-brand-blue text-white px-3 py-1 rounded-full text-sm mb-4 inline-block">
                                    POPULAIRE
                                </div>
                            )}

                            <div className="text-brand-blue mb-6">
                                {pack.icon}
                            </div>

                            <h3 className="text-xl font-bold mb-4">{pack.title}</h3>
                            <p className="text-brand-muted mb-6">{pack.description}</p>

                            <ul className="text-left space-y-2 text-brand-muted mb-8 flex-grow">
                                {pack.features.map((feature, index) => (
                                    <li key={index} className="flex items-start">
                                        <span className="text-brand-blue mr-2">•</span>
                                        {feature}
                                    </li>
                                ))}
                            </ul>

                            <div className="text-2xl font-bold text-brand-blue mb-6">{pack.price}</div>

                            <div className="space-y-3 mt-auto">
                                {pack.id !== 'availability' ? (
                                    <>
                                        <Link
                                            to={`/assistance/pack-${pack.id}`}
                                            className="bg-brand-surface text-brand-blue border border-brand-blue px-8 py-3 rounded-lg font-semibold hover:bg-brand-blue hover:text-white transition-all duration-300 inline-block w-full text-center"
                                        >
                                            En savoir plus
                                        </Link>
                                        <a
                                            href={pack.paypalLink}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="bg-gradient-to-r from-brand-blue to-brand-purple text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-brand-purple/25 transition-all duration-300 inline-block w-full text-center"
                                        >
                                            Commander
                                        </a>
                                    </>
                                ) : (
                                    <>
                                        <div className="bg-brand-surface text-brand-muted px-8 py-3 rounded-lg font-semibold inline-block w-full text-center border border-brand-surface">
                                            Informations pratiques
                                        </div>
                                        <a
                                            href="#contact"
                                            className="bg-gradient-to-r from-brand-blue to-brand-purple text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-brand-purple/25 transition-all duration-300 inline-block w-full text-center"
                                        >
                                            Me Contacter
                                        </a>
                                    </>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </section>

            {/* Pourquoi choisir notre assistance */}
            <section className="mb-20">
                <h2 className="text-3xl font-bold text-center mb-16">Pourquoi choisir notre assistance ?</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div className="text-center">
                        <div className="neon-card-sm mb-4">
                            <Clock className="w-12 h-12 text-brand-blue mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">Réactivité 24h</h3>
                            <p className="text-brand-muted text-sm">
                                Réponse garantie sous 24h pour tous vos besoins d'assistance
                            </p>
                        </div>
                    </div>
                    
                    <div className="text-center">
                        <div className="neon-card-sm mb-4">
                            <Shield className="w-12 h-12 text-brand-blue mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">Qualité garantie</h3>
                            <p className="text-brand-muted text-sm">
                                Satisfaction client garantie avec suivi post-intervention
                            </p>
                        </div>
                    </div>
                    
                    <div className="text-center">
                        <div className="neon-card-sm mb-4">
                            <Users className="w-12 h-12 text-brand-blue mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">Expertise reconnue</h3>
                            <p className="text-brand-muted text-sm">
                                Plus de 5 ans d'expérience en développement web et IA
                            </p>
                        </div>
                    </div>
                    
                    <div className="text-center">
                        <div className="neon-card-sm mb-4">
                            <Zap className="w-12 h-12 text-brand-blue mx-auto mb-4" />
                            <h3 className="font-semibold mb-2">Solutions modernes</h3>
                            <p className="text-brand-muted text-sm">
                                Technologies de pointe et bonnes pratiques du développement
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Section Partenaires Agences */}
            <section id="partenaires-section" className="mb-20">
                <h2 className="text-3xl font-bold text-center mb-6">Nos Partenaires Agences Intérim</h2>
                <p className="text-center text-brand-muted mb-16 max-w-3xl mx-auto">
                    Pour les entreprises, je collabore avec un réseau d'agences partenaires reconnues
                    dans le secteur IT. Ces partenariats garantissent un processus professionnel et sécurisé
                    pour vos projets d'envergure.
                </p>

                {/* Filtres par catégorie */}
                <div className="flex flex-wrap justify-center gap-4 mb-12">
                    <div className="bg-brand-blue/20 text-brand-blue px-4 py-2 rounded-full text-sm font-medium">
                        Intérim Généraliste IT
                    </div>
                    <div className="bg-brand-purple/20 text-brand-purple px-4 py-2 rounded-full text-sm font-medium">
                        Cabinet Spécialisé Tech
                    </div>
                    <div className="bg-brand-accent/20 text-brand-accent px-4 py-2 rounded-full text-sm font-medium">
                        Ultra-Spécialisé Front-End
                    </div>
                </div>

                {/* Grille des partenaires */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                    {partners.map((partner, index) => (
                        <div key={index} className="neon-card">
                            {/* Logo */}
                                <div className="flex justify-center mb-4">
                                    <div className="w-24 h-24 flex items-center justify-center bg-white rounded-lg p-2">
                                        <img
                                            src={partner.logo}
                                            alt={`Logo ${partner.name}`}
                                            className="max-w-full max-h-full object-contain"
                                            onError={(e) => {
                                                const target = e.target as HTMLImageElement;
                                                target.style.display = 'none';
                                                const parent = target.parentElement;
                                                if (parent) {
                                                    parent.innerHTML = `<div class="text-brand-blue font-bold text-sm text-center">${partner.name}</div>`;
                                                }
                                            }}
                                        />
                                    </div>
                                </div>

                                {/* Informations */}
                                <div className="text-center">
                                    <h3 className="text-lg font-semibold text-white mb-2">{partner.name}</h3>
                                    <div className={`inline-block px-3 py-1 rounded-full text-xs font-medium mb-3 ${
                                        partner.category === 'Intérim Généraliste IT' ? 'bg-brand-blue/20 text-brand-blue' :
                                        partner.category === 'Cabinet Spécialisé Tech' ? 'bg-brand-purple/20 text-brand-purple' :
                                        'bg-brand-accent/20 text-brand-accent'
                                    }`}>
                                        {partner.category}
                                    </div>
                                    <p className="text-brand-muted text-sm mb-4 leading-relaxed">
                                        {partner.description}
                                    </p>

                                    {/* Informations de contact */}
                                    <div className="space-y-2 text-xs text-brand-muted">
                                        {partner.locations && (
                                            <div className="flex items-center justify-center">
                                                <MapPin className="w-3 h-3 mr-1" />
                                                <span>{partner.locations.join(', ')}</span>
                                            </div>
                                        )}
                                        {partner.phone && (
                                            <div className="flex items-center justify-center">
                                                <Phone className="w-3 h-3 mr-1" />
                                                <span>{partner.phone}</span>
                                            </div>
                                        )}
                                        {partner.email && (
                                            <div className="flex items-center justify-center">
                                                <Mail className="w-3 h-3 mr-1" />
                                                <span>{partner.email}</span>
                                            </div>
                                        )}
                                    </div>

                                    {/* Lien vers le site */}
                                    {partner.website && (
                                        <div className="mt-4">
                                            <a
                                                href={partner.website}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="inline-flex items-center text-brand-blue hover:text-brand-purple transition-colors duration-300 text-sm font-medium"
                                            >
                                                Visiter le site
                                                <ExternalLink className="w-3 h-3 ml-1" />
                                            </a>
                                        </div>
                                    )}
                            </div>
                        </div>
                    ))}
                </div>

                {/* Note explicative */}
                <div className="mt-12 text-center">
                    <div className="neon-card max-w-4xl mx-auto">
                        <h3 className="text-xl font-semibold mb-4 text-brand-blue">Processus de collaboration</h3>
                        <p className="text-brand-muted leading-relaxed">
                            Pour les projets d'entreprise, le processus passe obligatoirement par l'une de ces agences partenaires.
                            Cela garantit un cadre professionnel sécurisé, une facturation en bonne et due forme,
                            et le respect de toutes les obligations légales et contractuelles.
                        </p>
                        <div className="mt-6">
                            <Link
                                to="/contact"
                                className="inline-flex items-center bg-gradient-to-r from-brand-blue to-brand-purple text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-brand-purple/25 transition-all duration-300"
                            >
                                Demander un devis entreprise
                                <ExternalLink className="w-4 h-4 ml-2" />
                            </Link>
                        </div>
                    </div>
                </div>
            </section>

            {/* Processus d'intervention */}
            <section className="mb-20">
                <h2 className="text-3xl font-bold text-center mb-16">Notre processus d'intervention</h2>
                <div className="max-w-4xl mx-auto">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div className="text-center">
                            <div className="neon-card-sm">
                                <div className="text-3xl mb-4">1️⃣</div>
                                <h3 className="font-semibold mb-2">Analyse</h3>
                                <p className="text-brand-muted text-sm">
                                    Diagnostic complet de votre problématique
                                </p>
                            </div>
                        </div>

                        <div className="text-center">
                            <div className="neon-card-sm">
                                <div className="text-3xl mb-4">2️⃣</div>
                                <h3 className="font-semibold mb-2">Planification</h3>
                                <p className="text-brand-muted text-sm">
                                    Élaboration d'un plan d'action détaillé
                                </p>
                            </div>
                        </div>

                        <div className="text-center">
                            <div className="neon-card-sm">
                                <div className="text-3xl mb-4">3️⃣</div>
                                <h3 className="font-semibold mb-2">Intervention</h3>
                                <p className="text-brand-muted text-sm">
                                    Mise en œuvre des solutions avec suivi
                                </p>
                            </div>
                        </div>

                        <div className="text-center">
                            <div className="neon-card-sm">
                                <div className="text-3xl mb-4">4️⃣</div>
                                <h3 className="font-semibold mb-2">Validation</h3>
                                <p className="text-brand-muted text-sm">
                                    Tests et validation avec documentation
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="text-center">
                <div className="neon-card">
                    <h2 className="text-3xl font-bold mb-6">Besoin d'aide avec votre projet ?</h2>
                    <p className="text-xl text-brand-muted mb-8 max-w-2xl mx-auto">
                        N'hésitez pas à me contacter pour discuter de vos besoins spécifiques.
                        Je vous propose toujours la solution la plus adaptée à votre situation.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Link
                            to="/contact"
                            className="bg-gradient-to-r from-brand-blue to-brand-purple text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-brand-purple/25 transition-all duration-300"
                        >
                            Demander un devis gratuit
                        </Link>
                        <Link
                            to="/portfolio"
                            className="border border-brand-surface text-brand-muted hover:text-white hover:border-brand-purple px-8 py-3 rounded-lg font-semibold transition-all duration-300"
                        >
                            Voir mes réalisations
                        </Link>
                    </div>
                </div>
            </section>
        </div>
    );
};

export default Assistance;
