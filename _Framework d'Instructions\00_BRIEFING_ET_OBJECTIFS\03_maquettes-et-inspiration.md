# **03_MAQUETTES-ET-INSPIRATION.MD - RÉFÉRENCES VISUELLES ET DESIGN**

## **OBJECTIF DU DOCUMENT**

Ce document centralise toutes les références visuelles, maquettes et sources d'inspiration pour guider le développement de l'interface utilisateur. Il sert de référence unique pour maintenir la cohérence visuelle du projet.

---

## **1. MAQUETTES OFFICIELLES**

### **1.1 Maquettes Principales**

**Maquette 1 - [Nom de la page/section]**
- **URL/Fichier :** [Lien vers la maquette]
- **Description :** [Description de la page/section]
- **Résolution :** [Dimensions de la maquette]
- **Statut :** [Validée/En cours/À réviser]
- **Notes spécifiques :**
  - [Point d'attention 1]
  - [Point d'attention 2]

**Maquette 2 - [Nom de la page/section]**
[Même structure que ci-dessus]

### **1.2 Maquettes Responsive**

**Mobile (< 640px)**
- **Maquette mobile 1 :** [Lien]
- **Maquette mobile 2 :** [Lien]
- **Points d'attention mobile :**
  - Navigation adaptée (hamburger menu, etc.)
  - Taille des éléments tactiles (min 44px)
  - Lisibilité du texte
  - Optimisation des images

**Tablet (640px - 1024px)**
- **Maquette tablet 1 :** [Lien]
- **Maquette tablet 2 :** [Lien]
- **Adaptations tablet :**
  - Grilles adaptatives
  - Navigation hybride
  - Optimisation de l'espace

**Desktop (> 1024px)**
- **Maquette desktop 1 :** [Lien]
- **Maquette desktop 2 :** [Lien]
- **Spécificités desktop :**
  - Navigation complète
  - Utilisation optimale de l'espace
  - Interactions avancées (hover, etc.)

---

## **2. DESIGN SYSTEM ET IDENTITÉ VISUELLE**

### **2.1 Palette de Couleurs**

**Couleurs Principales :**
```css
/* Couleurs primaires */
--primary-50: #[code couleur];
--primary-100: #[code couleur];
--primary-500: #[code couleur]; /* Couleur principale */
--primary-900: #[code couleur];

/* Couleurs secondaires */
--secondary-50: #[code couleur];
--secondary-500: #[code couleur];
--secondary-900: #[code couleur];

/* Couleurs neutres */
--gray-50: #[code couleur];
--gray-100: #[code couleur];
--gray-500: #[code couleur];
--gray-900: #[code couleur];

/* Couleurs sémantiques */
--success: #[code couleur];
--warning: #[code couleur];
--error: #[code couleur];
--info: #[code couleur];
```

**Utilisation des couleurs :**
- **Primaire :** Boutons principaux, liens, éléments d'action
- **Secondaire :** Boutons secondaires, accents
- **Neutre :** Textes, bordures, arrière-plans
- **Sémantique :** Messages d'état, notifications

### **2.2 Typographie**

**Polices Sélectionnées :**
- **Police principale :** [Nom de la police] - [Source/CDN]
  - Usage : Titres, textes importants
  - Poids disponibles : [300, 400, 600, 700]

- **Police secondaire :** [Nom de la police] - [Source/CDN]
  - Usage : Texte courant, paragraphes
  - Poids disponibles : [400, 500]

- **Police monospace :** [Nom de la police] - [Source/CDN]
  - Usage : Code, données techniques

**Hiérarchie typographique :**
```css
/* Titres */
h1: font-size: [taille]rem; font-weight: [poids]; line-height: [hauteur];
h2: font-size: [taille]rem; font-weight: [poids]; line-height: [hauteur];
h3: font-size: [taille]rem; font-weight: [poids]; line-height: [hauteur];

/* Texte courant */
body: font-size: [taille]rem; font-weight: [poids]; line-height: [hauteur];
small: font-size: [taille]rem; font-weight: [poids]; line-height: [hauteur];
```

### **2.3 Espacements et Grilles**

**Système d'espacement :**
- **Base :** 4px (0.25rem)
- **Échelle :** 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px
- **Marges internes :** Multiples de 4px
- **Marges externes :** Multiples de 8px

**Grille responsive :**
- **Mobile :** 1 colonne, padding 16px
- **Tablet :** 2-3 colonnes, padding 24px
- **Desktop :** 3-4 colonnes, padding 32px

---

## **3. COMPOSANTS UI STANDARDS**

### **3.1 Boutons**

**Types de boutons requis :**
- **Primaire :** Action principale (CTA)
- **Secondaire :** Actions secondaires
- **Tertiaire :** Actions subtiles (liens)
- **Danger :** Actions destructives
- **Disabled :** États désactivés

**États des boutons :**
- **Default :** État par défaut
- **Hover :** Survol (desktop uniquement)
- **Active :** Clic/tap
- **Focus :** Navigation clavier
- **Loading :** Chargement en cours
- **Disabled :** Désactivé

### **3.2 Formulaires**

**Éléments de formulaire :**
- **Input text :** Saisie de texte simple
- **Textarea :** Saisie de texte long
- **Select :** Liste déroulante
- **Checkbox :** Cases à cocher
- **Radio :** Boutons radio
- **Switch :** Interrupteurs

**États des champs :**
- **Default :** État par défaut
- **Focus :** Champ actif
- **Filled :** Champ rempli
- **Error :** Erreur de validation
- **Success :** Validation réussie
- **Disabled :** Champ désactivé

### **3.3 Navigation**

**Éléments de navigation :**
- **Header :** Navigation principale
- **Sidebar :** Navigation latérale (si applicable)
- **Breadcrumb :** Fil d'Ariane
- **Pagination :** Navigation entre pages
- **Tabs :** Onglets de contenu

---

## **4. SOURCES D'INSPIRATION**

### **4.1 Sites de Référence**

**Site 1 - [Nom du site]**
- **URL :** [Lien vers le site]
- **Éléments inspirants :**
  - [Aspect spécifique 1]
  - [Aspect spécifique 2]
- **À adapter :** [Ce qui doit être modifié pour notre contexte]

**Site 2 - [Nom du site]**
[Même structure]

### **4.2 Tendances Design**

**Tendances à intégrer :**
- **[Tendance 1] :** [Description et application]
- **[Tendance 2] :** [Description et application]

**Tendances à éviter :**
- **[Anti-pattern 1] :** [Raison de l'évitement]
- **[Anti-pattern 2] :** [Raison de l'évitement]

### **4.3 Références Sectorielles**

**Concurrents directs :**
- **[Concurrent 1] :** [Points forts à retenir]
- **[Concurrent 2] :** [Points forts à retenir]

**Bonnes pratiques sectorielles :**
- [Pratique 1 spécifique au secteur]
- [Pratique 2 spécifique au secteur]

---

## **5. ASSETS ET RESSOURCES**

### **5.1 Images et Illustrations**

**Sources d'images :**
- **Photos :** [Source/banque d'images utilisée]
- **Illustrations :** [Style et source des illustrations]
- **Icônes :** [Bibliothèque d'icônes choisie]
- **Logos :** [Fichiers et variations du logo]

**Spécifications techniques :**
- **Formats :** WebP (priorité), PNG, JPG
- **Résolutions :** 1x, 2x, 3x pour les écrans haute densité
- **Optimisation :** Compression et lazy loading

### **5.2 Animations et Interactions**

**Types d'animations :**
- **Micro-interactions :** Feedback sur les actions utilisateur
- **Transitions :** Changements d'état fluides
- **Animations d'entrée :** Apparition des éléments
- **Loading animations :** Indicateurs de chargement

**Principes d'animation :**
- **Durée :** 200-300ms pour les micro-interactions
- **Easing :** Courbes naturelles (ease-out, ease-in-out)
- **Performance :** 60fps minimum, optimisation GPU

---

## **6. VALIDATION ET CONFORMITÉ**

### **6.1 Checklist Design**

**Validation visuelle :**
- [ ] Cohérence avec l'identité de marque
- [ ] Respect de la hiérarchie visuelle
- [ ] Lisibilité sur tous les supports
- [ ] Accessibilité des couleurs (contraste)
- [ ] Responsive design fonctionnel

### **6.2 Tests Utilisateur**

**Critères d'évaluation :**
- **Intuitivité :** Navigation naturelle
- **Efficacité :** Accomplissement des tâches
- **Satisfaction :** Expérience utilisateur positive
- **Accessibilité :** Utilisabilité pour tous

---

## **7. ÉVOLUTION ET MAINTENANCE**

### **7.1 Versioning du Design**

**Gestion des versions :**
- Version actuelle : [X.Y]
- Historique des modifications
- Validation des changements par Cisco

### **7.2 Documentation Vivante**

**Mise à jour continue :**
- Ajout de nouveaux composants
- Évolution des standards
- Retours d'expérience utilisateur
- Optimisations identifiées

---

**⚠️ IMPORTANT :** Ce document doit être maintenu à jour tout au long du projet. Toute modification du design doit être documentée et validée par Cisco avant implémentation.
