<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlexoDiv Apps - Applications Web</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #1B1C1D;
        }

        /* Effet néon pour les cartes */
        .glowing-card {
            position: relative;
            background: linear-gradient(135deg, #282A2C 0%, #1B1C1D 100%);
            border: 1px solid rgba(33, 144, 246, 0.3);
            border-radius: 1rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .glowing-card:hover {
            border-color: rgba(33, 144, 246, 0.6);
            box-shadow:
                0 0 20px rgba(33, 144, 246, 0.3),
                0 0 40px rgba(33, 144, 246, 0.1),
                inset 0 0 20px rgba(33, 144, 246, 0.05);
            transform: translateY(-5px);
        }

        .glowing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(33, 144, 246, 0.1), transparent);
            transition: left 0.5s;
        }

        .glowing-card:hover::before {
            left: 100%;
        }

        /* Gradient Gemini */
        .gemini-gradient {
            background: linear-gradient(90deg, #1A3452 0%, #2190F6 25%, #6689EF 50%, #8D86ED 75%, #AE87F3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Effet 3D sur les titres */
        .title-3d {
            text-shadow:
                2px 2px 0px #000,
                4px 4px 8px rgba(0,0,0,0.5);
        }

        /* Animation d'apparition */
        .fade-in {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .fade-in:nth-child(1) { animation-delay: 0.1s; }
        .fade-in:nth-child(2) { animation-delay: 0.2s; }
        .fade-in:nth-child(3) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Bouton avec effet néon */
        .neon-button {
            background: linear-gradient(45deg, #2190F6, #6689EF);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(33, 144, 246, 0.3);
        }

        .neon-button:hover {
            box-shadow:
                0 0 20px rgba(33, 144, 246, 0.6),
                0 4px 25px rgba(33, 144, 246, 0.4);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="min-h-screen text-white">

    <!-- Header -->
    <header class="py-12 text-center">
        <div class="container mx-auto px-6">
            <h1 class="text-5xl font-bold title-3d mb-4">
                Flexo<span class="gemini-gradient">Div</span> Apps
            </h1>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                Collection d'applications web innovantes développées avec l'IA.
                Découvrez nos outils créés pour simplifier votre quotidien professionnel.
            </p>
        </div>
    </header>

    <!-- Applications Grid -->
    <main class="container mx-auto px-6 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="appsGrid">

            <!-- App 1: Info-Société Pro -->
            <div class="glowing-card fade-in">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-white">Info-Société Pro</h3>
                    </div>

                    <p class="text-gray-300 mb-6 leading-relaxed">
                        Recherchez des informations complètes sur n'importe quelle entreprise et générez automatiquement des accroches personnalisées avec l'IA Gemini.
                    </p>

                    <div class="flex flex-wrap gap-2 mb-6">
                        <span class="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">Recherche</span>
                        <span class="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm">IA Gemini</span>
                        <span class="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">Prospection</span>
                    </div>

                    <a href="Info-Société-Pro.html" target="_blank" class="neon-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15,3 21,3 21,9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                        Lancer l'app
                    </a>
                </div>
            </div>

            <!-- Placeholder pour futures apps -->
            <div class="glowing-card fade-in opacity-50">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-gray-500 to-gray-600 rounded-lg flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-400">Prochaine App</h3>
                    </div>

                    <p class="text-gray-500 mb-6 leading-relaxed">
                        Une nouvelle application innovante arrive bientôt. Restez connecté pour découvrir nos prochains outils IA.
                    </p>

                    <div class="flex flex-wrap gap-2 mb-6">
                        <span class="px-3 py-1 bg-gray-500/20 text-gray-400 rounded-full text-sm">Bientôt</span>
                    </div>

                    <button disabled class="neon-button opacity-50 cursor-not-allowed">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="10,15 15,10 21,16"></polyline>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                        En développement
                    </button>
                </div>
            </div>

        </div>
    </main>

    <!-- Footer -->
    <footer class="text-center py-12 border-t border-gray-700 mt-20">
        <div class="container mx-auto px-6">
            <p class="text-gray-400 mb-2">
                Propulsé par <span class="gemini-gradient font-semibold">Gemini AI</span> & <span class="text-blue-400 font-semibold">FlexoDiv</span>
            </p>
            <p class="text-sm text-gray-500">
                &copy; <span id="currentYear"></span> FlexoDiv - Francisco. Tous droits réservés.
            </p>
        </div>
    </footer>

    <script>
        // Année dynamique
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Animation au scroll (optionnel)
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>