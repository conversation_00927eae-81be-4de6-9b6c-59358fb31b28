# **05_PROCEDURES-URGENCE.MD - GESTION DES INCIDENTS**

## **OBJECTIF DES PROCÉDURES D'URGENCE**

Ce document définit les procédures d'intervention d'urgence pour gérer les incidents critiques en production. Ces procédures garantissent une réponse rapide, efficace et coordonnée face aux situations de crise.

---

## **1. CLASSIFICATION DES INCIDENTS**

### **1.1 Niveaux de Criticité**

**Classification obligatoire :**

**🔴 CRITIQUE (P0) - Intervention immédiate**
- Application complètement inaccessible
- Perte de données utilisateur
- Faille de sécurité majeure
- Temps de résolution cible : < 1 heure

**🟠 MAJEUR (P1) - Intervention urgente**
- Fonctionnalité principale indisponible
- Performance dégradée significativement
- Erreurs affectant > 50% des utilisateurs
- Temps de résolution cible : < 4 heures

**🟡 MINEUR (P2) - Intervention prioritaire**
- Fonctionnalité secondaire défaillante
- Problème affectant < 25% des utilisateurs
- Dégradation de performance modérée
- Temps de résolution cible : < 24 heures

**🟢 COSMÉTIQUE (P3) - Intervention planifiée**
- Problème d'affichage mineur
- Amélioration UX
- Bug sans impact utilisateur
- Temps de résolution cible : < 1 semaine

### **1.2 Matrice d'Escalade**

**Processus d'escalade automatique :**
```
P0 (Critique) → Notification immédiate → Équipe complète mobilisée
P1 (Majeur)   → Notification < 15 min → Lead technique + DevOps
P2 (Mineur)   → Notification < 1h     → Développeur assigné
P3 (Cosmétique) → Ticket créé        → Backlog de maintenance
```

---

## **2. PROCÉDURE D'INTERVENTION D'URGENCE**

### **2.1 Phase de Détection**

**Canaux de détection :**
- **Monitoring automatique** : Alertes système
- **Utilisateurs** : Signalements support
- **Équipe** : Observation directe
- **Tests automatisés** : Échecs en production

**Actions immédiates :**
```bash
# 1. Vérification rapide du statut
curl -I https://your-app.com
curl -I https://your-app.com/api/health

# 2. Consultation des logs en temps réel
# Netlify
netlify logs --site=your-site-id --live

# Firebase
firebase functions:log --project=your-project-id

# 3. Vérification des métriques
# Accès aux dashboards de monitoring
```

### **2.2 Phase d'Évaluation**

**Checklist d'évaluation rapide (< 5 minutes) :**
- [ ] **Scope** : Nombre d'utilisateurs affectés
- [ ] **Impact** : Fonctionnalités indisponibles
- [ ] **Cause** : Hypothèse initiale (déploiement, infrastructure, code)
- [ ] **Tendance** : Problème qui s'aggrave ou se stabilise
- [ ] **Criticité** : Classification P0/P1/P2/P3

### **2.3 Phase de Communication**

**Communication d'incident obligatoire :**
```typescript
// Template de notification d'incident
interface IncidentNotification {
  id: string
  severity: 'P0' | 'P1' | 'P2' | 'P3'
  title: string
  description: string
  impact: string
  startTime: Date
  estimatedResolution?: Date
  status: 'investigating' | 'identified' | 'monitoring' | 'resolved'
  updates: IncidentUpdate[]
}

// Exemple de notification
const incidentNotification: IncidentNotification = {
  id: 'INC-2024-001',
  severity: 'P1',
  title: 'Lenteur de chargement des pages',
  description: 'Les utilisateurs rapportent des temps de chargement > 10s',
  impact: 'Tous les utilisateurs affectés, fonctionnalités disponibles mais lentes',
  startTime: new Date(),
  estimatedResolution: new Date(Date.now() + 2 * 60 * 60 * 1000), // +2h
  status: 'investigating',
  updates: []
}
```

---

## **3. PROCÉDURES DE ROLLBACK**

### **3.1 Rollback Netlify**

**Procédure de rollback immédiat :**
```bash
# 1. Lister les déploiements récents
netlify api listSiteDeploys --data='{"site_id":"your-site-id"}'

# 2. Identifier le dernier déploiement stable
# (noter le deploy_id du déploiement précédent)

# 3. Restaurer le déploiement stable
netlify api restoreSiteDeploy --data='{"site_id":"your-site-id","deploy_id":"previous-deploy-id"}'

# 4. Vérifier la restauration
curl -I https://your-app.com
```

**Rollback via interface web :**
1. Aller sur https://app.netlify.com/sites/your-site/deploys
2. Identifier le dernier déploiement stable
3. Cliquer sur "Publish deploy" sur le déploiement stable
4. Confirmer la publication

### **3.2 Rollback Firebase**

**Procédure de rollback Firebase Functions :**
```bash
# 1. Lister les versions déployées
firebase functions:list --project=your-project-id

# 2. Rollback vers la version précédente
firebase deploy --only functions --project=your-project-id

# Alternative : déployer une version spécifique
git checkout previous-stable-commit
firebase deploy --only functions --project=your-project-id
```

### **3.3 Rollback Base de Données**

**⚠️ ATTENTION : Procédure critique**
```bash
# 1. ARRÊTER IMMÉDIATEMENT les écritures
# Activer le mode maintenance si possible

# 2. Évaluer la faisabilité du rollback
# - Perte de données acceptable ?
# - Migrations réversibles ?
# - Impact sur les utilisateurs ?

# 3. Restauration depuis sauvegarde (si nécessaire)
gcloud firestore import gs://your-backup-bucket/firestore/latest \
  --project=your-project-id

# 4. Vérification de l'intégrité
npm run test:data-integrity
```

---

## **4. DIAGNOSTIC ET RÉSOLUTION**

### **4.1 Outils de Diagnostic**

**Commandes de diagnostic essentielles :**
```bash
# Vérification de l'état des services
curl -f https://your-app.com/api/health || echo "API indisponible"

# Analyse des logs d'erreur
# Netlify
netlify logs --site=your-site-id | grep -i error

# Firebase
firebase functions:log --project=your-project-id | grep -i error

# Vérification des métriques de performance
# Utiliser les dashboards de monitoring configurés

# Test de connectivité réseau
ping your-app.com
nslookup your-app.com
traceroute your-app.com
```

### **4.2 Checklist de Diagnostic**

**Diagnostic systématique (ordre de priorité) :**
- [ ] **Frontend** : Application accessible, erreurs JavaScript
- [ ] **API** : Endpoints fonctionnels, temps de réponse
- [ ] **Base de données** : Connectivité, performance des requêtes
- [ ] **Authentification** : Connexion utilisateur, tokens valides
- [ ] **CDN/Cache** : Invalidation cache, distribution contenu
- [ ] **DNS** : Résolution nom de domaine
- [ ] **Certificats SSL** : Validité et expiration

### **4.3 Patterns de Résolution**

**Solutions rapides par type d'incident :**

**Problème de performance :**
```typescript
// 1. Identifier les requêtes lentes
// 2. Optimiser les requêtes critiques
// 3. Activer la mise en cache
// 4. Réduire la taille des réponses

// Exemple d'optimisation rapide
const optimizeQuery = async () => {
  // Avant : requête non optimisée
  // const users = await db.collection('users').get()
  
  // Après : requête optimisée avec limite
  const users = await db.collection('users')
    .limit(10)
    .select(['id', 'name', 'email'])
    .get()
}
```

**Erreur d'authentification :**
```typescript
// 1. Vérifier la validité des tokens
// 2. Contrôler les règles Firebase
// 3. Valider la configuration OAuth

// Solution temporaire : bypass auth pour diagnostic
const temporaryAuthBypass = () => {
  if (process.env.NODE_ENV === 'development' && process.env.EMERGENCY_MODE) {
    return { uid: 'emergency-user', email: '<EMAIL>' }
  }
  return null
}
```

---

## **5. COMMUNICATION DE CRISE**

### **5.1 Canaux de Communication**

**Hiérarchie de communication :**
1. **Status page** : Mise à jour automatique du statut
2. **Notifications push** : Alertes utilisateurs (si applicable)
3. **Email** : Communication aux utilisateurs critiques
4. **Réseaux sociaux** : Information publique si nécessaire
5. **Support client** : Scripts de réponse préparés

### **5.2 Templates de Communication**

**Template de communication utilisateur :**
```markdown
# 🚨 Incident en cours - [Titre]

**Statut :** En cours d'investigation
**Impact :** [Description de l'impact]
**Heure de début :** [Timestamp]
**Estimation de résolution :** [Estimation]

## Ce que nous faisons
- [Action 1 en cours]
- [Action 2 planifiée]

## Contournement temporaire
[Instructions si disponibles]

Nous vous tiendrons informés toutes les 30 minutes.

---
Dernière mise à jour : [Timestamp]
```

### **5.3 Post-Mortem Obligatoire**

**Template de post-mortem :**
```markdown
# Post-Mortem - [Incident ID] - [Date]

## Résumé
- **Durée :** [Début] - [Fin] ([Durée totale])
- **Impact :** [Nombre d'utilisateurs affectés]
- **Cause racine :** [Cause identifiée]

## Chronologie
- [Timestamp] : Incident détecté
- [Timestamp] : Équipe mobilisée
- [Timestamp] : Cause identifiée
- [Timestamp] : Solution appliquée
- [Timestamp] : Incident résolu

## Cause Racine
[Analyse détaillée de la cause]

## Actions Correctives
### Immédiates
- [ ] [Action 1]
- [ ] [Action 2]

### Long terme
- [ ] [Action 1]
- [ ] [Action 2]

## Leçons Apprises
- [Leçon 1]
- [Leçon 2]

## Améliorations du Processus
- [Amélioration 1]
- [Amélioration 2]
```

---

## **6. TESTS DE PROCÉDURES D'URGENCE**

### **6.1 Exercices de Simulation**

**Planning des exercices obligatoires :**
- **Mensuel** : Test de rollback sur environnement de staging
- **Trimestriel** : Simulation d'incident complet
- **Semestriel** : Test de récupération après sinistre

### **6.2 Checklist de Préparation**

**Préparation de l'équipe :**
- [ ] Contacts d'urgence à jour
- [ ] Accès aux outils de production vérifiés
- [ ] Procédures imprimées et accessibles
- [ ] Environnement de test configuré
- [ ] Scripts de rollback testés

---

## **7. AMÉLIORATION CONTINUE**

### **7.1 Métriques d'Incident**

**KPIs à suivre :**
- **MTTR** (Mean Time To Recovery) : < 1h pour P0, < 4h pour P1
- **MTBF** (Mean Time Between Failures) : > 30 jours
- **Taux de rollback** : < 5% des déploiements
- **Satisfaction client** : > 95% après résolution

### **7.2 Révision des Procédures**

**Révision obligatoire après chaque incident P0/P1 :**
- Efficacité des procédures
- Temps de réponse
- Qualité de la communication
- Améliorations possibles

---

## **8. CONTACTS D'URGENCE**

### **8.1 Escalade Technique**

```
Niveau 1 : Développeur de garde
- Téléphone : [Numéro]
- Email : [Email]
- Slack : @dev-oncall

Niveau 2 : Lead Technique
- Téléphone : [Numéro]
- Email : [Email]
- Slack : @tech-lead

Niveau 3 : CTO/Architecte
- Téléphone : [Numéro]
- Email : [Email]
- Slack : @cto
```

### **8.2 Contacts Externes**

```
Netlify Support : <EMAIL>
Firebase Support : <EMAIL>
DNS Provider : [Contact]
CDN Provider : [Contact]
```

---

**🚨 CRITIQUE :** Ces procédures d'urgence sont vitales pour la continuité de service. Elles DOIVENT être connues de tous les membres de l'équipe et testées régulièrement. En cas d'incident, suivre ces procédures à la lettre peut faire la différence entre une interruption mineure et une crise majeure.
