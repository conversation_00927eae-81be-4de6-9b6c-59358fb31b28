<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Info-Société Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 1rem;
        }
        .modal-content {
            background: #1f2937; /* Fond sombre */
            color: #f9fafb; /* Texte clair */
            padding: 2rem;
            border-radius: 0.75rem;
            max-width: 600px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            border: 1px solid #374151; /* Bordure subtile */
        }

        /* Styles spécifiques pour le contenu de la modal */
        .modal-content h3 {
            color: #f9fafb !important;
            margin-bottom: 1rem;
        }
        .modal-content p {
            color: #e5e7eb !important;
            line-height: 1.6;
        }
        .modal-content ul {
            color: #e5e7eb !important;
            padding-left: 1.5rem;
        }
        .modal-content li {
            color: #e5e7eb !important;
            margin-bottom: 0.5rem;
            list-style-type: disc;
        }
        .modal-content .loader {
            border-top-color: #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 flex flex-col min-h-screen">

    <main class="flex-grow">
        <div class="container mx-auto p-4 sm:p-6 md:p-8">
            <div class="max-w-2xl mx-auto">
                <header class="text-center mb-8">
                    <h1 class="text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white">Info-Société Pro</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">Trouvez des infos et générez des accroches et analyses avec l'IA.</p>
                </header>

                <!-- Scénario d'utilisation pliable -->
                <div class="mb-6">
                    <button id="toggleScenario" class="w-full bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 border border-blue-200 dark:border-gray-600 rounded-lg p-3 text-left hover:from-blue-100 hover:to-purple-100 dark:hover:from-gray-700 dark:hover:to-gray-600 transition-all duration-300 flex items-center justify-between">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-blue-600 dark:text-blue-400">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                <line x1="12" y1="17" x2="12.01" y2="17"></line>
                            </svg>
                            <span class="font-medium text-gray-700 dark:text-gray-300">💡 Exemple concret : Comment décrocher une mission</span>
                        </div>
                        <svg id="scenarioArrow" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-500 transform transition-transform duration-300">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>

                    <div id="scenarioContent" class="hidden mt-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
                        <div class="prose dark:prose-invert max-w-none text-sm">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">🎯 Scénario : Décrocher une mission avec Info-Société Pro</h3>

                            <div class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4 mb-4">
                                <p class="text-gray-700 dark:text-gray-300"><strong>Situation :</strong> Vous êtes développeur freelance et avez entendu parler d'<em>"Innovatech Solutions"</em>. Vous voulez voir s'il y a des opportunités.</p>
                            </div>

                            <div class="space-y-4">
                                <div class="border-l-4 border-green-400 pl-4">
                                    <h4 class="font-semibold text-green-700 dark:text-green-400">📋 Étape 1 : Rechercher l'entreprise</h4>
                                    <p class="text-gray-600 dark:text-gray-400">Tapez <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">Innovatech Solutions</code> et cliquez "Rechercher".</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-500">→ Résultat : Fiche complète avec site web, email, téléphone, adresse, SIREN et description de l'activité.</p>
                                </div>

                                <div class="border-l-4 border-purple-400 pl-4">
                                    <h4 class="font-semibold text-purple-700 dark:text-purple-400">✨ Étape 2 : Générer une accroche</h4>
                                    <p class="text-gray-600 dark:text-gray-400">Cliquez sur <strong>"✨ Générer une accroche"</strong></p>
                                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded mt-2 text-xs">
                                        <p class="font-medium">Exemple de résultat :</p>
                                        <p class="italic">"Bonjour, en découvrant l'activité d'Innovatech Solutions, et notamment votre expertise en IA et blockchain pour la finance, je me permets de vous contacter..."</p>
                                    </div>
                                </div>

                                <div class="border-l-4 border-orange-400 pl-4">
                                    <h4 class="font-semibold text-orange-700 dark:text-orange-400">🎯 Étape 3 : Analyser les opportunités</h4>
                                    <p class="text-gray-600 dark:text-gray-400">Cliquez sur <strong>"✨ Analyser les opportunités"</strong></p>
                                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded mt-2 text-xs">
                                        <p class="font-medium">Suggestions de postes :</p>
                                        <ul class="list-disc list-inside text-gray-600 dark:text-gray-400">
                                            <li>Développeur Full-Stack (FinTech)</li>
                                            <li>Ingénieur Blockchain</li>
                                            <li>Chef de Projet Technique IA</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mt-4">
                                <p class="text-green-800 dark:text-green-200 font-medium">🚀 Résultat : En 2 minutes, vous passez d'un nom d'entreprise à une stratégie de prospection complète !</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <input type="text" id="companyName" class="flex-grow w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none" placeholder="Ex: Seyos Informatique...">
                        <button id="searchButton" class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-300 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                            Rechercher
                        </button>
                    </div>
                </div>

                <div id="results" class="space-y-6"></div>
            </div>
        </div>
    </main>

    <!-- Modal Structure (initially hidden) -->
    <div id="modal" class="modal-overlay hidden">
        <div class="modal-content dark:text-white">
            <h3 id="modalTitle" class="text-2xl font-bold mb-4"></h3>
            <div id="modalBody" class="prose dark:prose-invert max-w-none"></div>
            <div id="modalActions" class="mt-6 text-right"></div>
            <button id="closeModal" class="absolute top-4 right-4 text-gray-500 hover:text-gray-800 dark:hover:text-gray-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
            </button>
        </div>
    </div>
    
    <footer class="text-center py-6">
        <p class="text-sm text-gray-500 dark:text-gray-400">
            Propulsé par Gemini & FlexoDiv
        </p>
        <p id="copyright" class="text-xs text-gray-400 dark:text-gray-500 mt-1"></p>
    </footer>

    <script>
        const searchButton = document.getElementById('searchButton');
        const companyNameInput = document.getElementById('companyName');
        const resultsDiv = document.getElementById('results');
        const modal = document.getElementById('modal');
        const closeModal = document.getElementById('closeModal');

        let currentCompanyData = null;

        searchButton.addEventListener('click', findCompanyInfo);
        companyNameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') findCompanyInfo();
        });
        closeModal.addEventListener('click', () => modal.classList.add('hidden'));
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.classList.add('hidden');
        });

        async function callGeminiAPI(prompt) {
            const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

            // Configuration sécurisée de la clé API
            let apiKey = "";

            // Vérification du domaine autorisé
            const isAuthorizedDomain = window.location.hostname === 'flexodiv.netlify.app' ||
                                     window.location.hostname === 'www.flexodiv.netlify.app' ||
                                     window.location.hostname === 'localhost' ||
                                     window.location.hostname === '127.0.0.1';

            if (isAuthorizedDomain) {
                // Injecter la clé API de manière sécurisée pour les domaines autorisés
                apiKey = 'AIzaSyCqooMDW5sKHciKAX19JUolQT01RDhBysc';
            }

            try {
                // Essayer de récupérer depuis le parent (si intégré dans le site principal)
                if (!apiKey && window.parent && window.parent !== window) {
                    apiKey = window.parent.VITE_GEMINI_API_KEY || window.parent.GEMINI_API_KEY || "";
                }
                // Fallback : essayer depuis les variables globales injectées
                if (!apiKey && window.GEMINI_API_KEY) {
                    apiKey = window.GEMINI_API_KEY;
                }
            } catch (e) {
                console.warn("Impossible de récupérer la clé API depuis le parent");
            }

            if (!apiKey) {
                throw new Error("Clé API non disponible. Veuillez contacter l'administrateur.");
            }

            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!response.ok) throw new Error(`Erreur API: ${response.statusText}`);
            
            const result = await response.json();

            if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts[0]) {
                return result.candidates[0].content.parts[0].text;
            } else {
                throw new Error("Réponse de l'API invalide ou vide.");
            }
        }

        async function findCompanyInfo() {
            const companyName = companyNameInput.value.trim();
            if (!companyName) {
                showError("Veuillez entrer un nom d'entreprise.");
                return;
            }
            showLoader();
            try {
                const prompt = `Tu es un assistant spécialisé dans la recherche d'informations d'entreprises. Basé sur tes connaissances, fournis les informations disponibles sur l'entreprise "${companyName}".

IMPORTANT : Si tu ne connais pas une information spécifique, utilise des informations génériques plausibles basées sur le secteur d'activité de l'entreprise, mais indique clairement qu'il s'agit d'informations à vérifier.

Informations requises :
- nom : Le nom exact de l'entreprise
- siteWeb : Site web officiel (si connu, sinon propose un format probable comme www.[nom-entreprise].fr)
- email : Email de contact (si connu, sinon propose un format probable comme contact@[nom-entreprise].fr)
- telephone : Numéro de téléphone (si connu, sinon "À vérifier")
- description : Description détaillée de l'activité (minimum 2-3 phrases)
- adressePostale : Adresse complète (si connue, sinon "À vérifier")
- siren : Numéro SIREN/SIRET (si connu, sinon "À vérifier")
- reseauxSociaux : Objet avec linkedin, twitter, facebook (si connus, sinon "À vérifier")

Réponds UNIQUEMENT avec un objet JSON valide contenant ces clés exactes.`;

                const rawText = await callGeminiAPI(prompt);
                console.log("Réponse brute de l'API:", rawText); // Debug

                const jsonText = rawText.replace(/```json/g, '').replace(/```/g, '').trim();
                console.log("JSON nettoyé:", jsonText); // Debug

                currentCompanyData = JSON.parse(jsonText);
                console.log("Données parsées:", currentCompanyData); // Debug

                displayResults(currentCompanyData);
            } catch (error) {
                console.error("Erreur lors de la recherche:", error);
                console.error("Stack trace:", error.stack);
                showError(`Erreur: ${error.message}`);
            }
        }

        async function generatePitch() {
            if (!currentCompanyData) {
                console.error("Aucune donnée d'entreprise disponible");
                return;
            }

            console.log("Génération d'accroche pour:", currentCompanyData);
            showModal("Génération d'accroche...", '<div class="flex justify-center"><div class="loader"></div></div>');

            try {
                const prompt = `En te basant sur les informations de l'entreprise '${currentCompanyData.nom}' (description: ${currentCompanyData.description}), rédige un court e-mail de prise de contact (environ 100 mots). L'e-mail doit être professionnel, poli et exprimer un intérêt pour d'éventuelles missions d'intérim ou projets freelances. Je suis un(e) professionnel(le) de l'informatique. Termine par "Cordialement," et laisse un espace pour le nom. Réponds uniquement avec le texte de l'e-mail.`;

                const pitchText = await callGeminiAPI(prompt);
                console.log("Accroche générée:", pitchText);

                showModal("Proposition d'accroche", `<p>${pitchText.replace(/\n/g, '<br>')}</p>`, true, pitchText);
            } catch (error) {
                console.error("Erreur lors de la génération de l'accroche:", error);
                showModal("Erreur", `<p>Erreur: ${error.message}</p>`);
            }
        }

        async function analyzeOpportunities() {
            if (!currentCompanyData) {
                console.error("Aucune donnée d'entreprise disponible");
                return;
            }

            console.log("Analyse des opportunités pour:", currentCompanyData);
            showModal("Analyse des opportunités...", '<div class="flex justify-center"><div class="loader"></div></div>');

            try {
                const prompt = `Analyse la description de l'entreprise '${currentCompanyData.nom}': '${currentCompanyData.description}'.

En te basant sur cette description, liste exactement 5 types de postes ou de missions d'intérim pertinents qu'un professionnel de l'informatique pourrait rechercher dans cette entreprise.

Format de réponse souhaité :
- Poste 1
- Poste 2
- Poste 3
- Poste 4
- Poste 5

Réponds uniquement avec cette liste, rien d'autre.`;

                const opportunitiesText = await callGeminiAPI(prompt);
                console.log("Opportunités générées (brut):", opportunitiesText);

                // Meilleur parsing de la liste
                let htmlList = '';
                const lines = opportunitiesText.split('\n').filter(line => line.trim() !== '');

                if (lines.length > 0) {
                    htmlList = '<ul>';
                    lines.forEach(line => {
                        const cleanLine = line.replace(/^[-•*]\s*/, '').trim();
                        if (cleanLine) {
                            htmlList += `<li>${cleanLine}</li>`;
                        }
                    });
                    htmlList += '</ul>';
                } else {
                    // Fallback si le parsing échoue
                    htmlList = `<p>${opportunitiesText}</p>`;
                }

                console.log("HTML généré:", htmlList);
                showModal("Opportunités potentielles", htmlList);

            } catch (error) {
                console.error("Erreur lors de l'analyse des opportunités:", error);
                showModal("Erreur", `<p>Erreur: ${error.message}</p>`);
            }
        }

        function showModal(title, content, showCopyButton = false, textToCopy = '') {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            const actionsDiv = document.getElementById('modalActions');
            actionsDiv.innerHTML = '';
            if (showCopyButton) {
                const copyButton = document.createElement('button');
                copyButton.textContent = 'Copier le texte';
                copyButton.className = 'bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition-colors';
                copyButton.onclick = () => {
                    const textarea = document.createElement('textarea');
                    textarea.value = textToCopy;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    copyButton.textContent = 'Copié !';
                    setTimeout(() => { copyButton.textContent = 'Copier le texte'; }, 2000);
                };
                actionsDiv.appendChild(copyButton);
            }
            modal.classList.remove('hidden');
        }

        function showLoader() {
            resultsDiv.innerHTML = `<div class="flex flex-col items-center justify-center bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8"><div class="loader mb-4"></div><p class="text-gray-600 dark:text-gray-400">Recherche en cours...</p></div>`;
        }

        function displayResults(data) {
            resultsDiv.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                    <div class="p-6">
                        <div class="flex justify-between items-start">
                             <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">${data.nom || 'Nom non trouvé'}</h2>
                             <div class="flex space-x-3">${createSocialLinks(data.reseauxSociaux)}</div>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">${data.description || 'Description non disponible.'}</p>
                        <div class="space-y-4">
                            ${createInfoLine('🌐', 'Site Web', data.siteWeb, true)}
                            ${createInfoLine('✉️', 'Email', data.email, false, 'mailto:')}
                            ${createInfoLine('📞', 'Téléphone', data.telephone, false, 'tel:')}
                            ${createInfoLine('📍', 'Adresse', data.adressePostale)}
                            ${createInfoLine('🏢', 'SIREN/SIRET', data.siren)}
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700/50 p-4 flex flex-col sm:flex-row gap-3 justify-end">
                        <button onclick="generatePitch()" class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center">✨ Générer une accroche</button>
                        <button onclick="analyzeOpportunities()" class="bg-teal-600 hover:bg-teal-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center">✨ Analyser les opportunités</button>
                    </div>
                </div>`;
        }
        
        function createInfoLine(icon, label, value, isLink = false, linkPrefix = '') {
            if (!value || value.toLowerCase() === 'non trouvé') return `<div class="flex items-start"><span class="text-xl mr-4 mt-1">${icon}</span><div><p class="font-semibold text-gray-700 dark:text-gray-300">${label}</p><p class="text-gray-400 dark:text-gray-500">Non trouvé</p></div></div>`;
            const linkHref = isLink ? (value.startsWith('http') ? value : `https://${value}`) : (linkPrefix ? `${linkPrefix}${value}` : '#');
            const content = isLink || linkPrefix ? `<a href="${linkHref}" target="_blank" rel="noopener noreferrer" class="text-blue-500 hover:underline break-all">${value}</a>` : `<p class="text-gray-600 dark:text-gray-300 break-words">${value}</p>`;
            return `<div class="flex items-start"><span class="text-xl mr-4 mt-1">${icon}</span><div><p class="font-semibold text-gray-700 dark:text-gray-300">${label}</p>${content}</div></div>`;
        }

        function createSocialLinks(socialData) {
            if (!socialData) return '';
            let html = '';
            const socialIcons = {
                linkedin: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="text-gray-500 hover:text-blue-700"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>',
                twitter: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="text-gray-500 hover:text-blue-500"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24h-6.617l-5.21-6.817-6.044 6.817h-3.308l7.73-8.805-7.993-10.69h6.77l4.61 6.245 5.46-6.245zm-1.161 17.52h1.839l-10.124-13.6h-1.93l10.215 13.6z"/></svg>',
                facebook: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="text-gray-500 hover:text-blue-600"><path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v2.385z"/></svg>'
            };
            for (const [network, url] of Object.entries(socialData)) {
                if (url && url.toLowerCase() !== 'non trouvé' && socialIcons[network]) {
                    html += `<a href="${url}" target="_blank" rel="noopener noreferrer" title="${network}">${socialIcons[network]}</a>`;
                }
            }
            return html;
        }

        function showError(message) {
            resultsDiv.innerHTML = `<div class="bg-red-100 dark:bg-red-900 border-l-4 border-red-500 text-red-700 dark:text-red-200 p-4 rounded-lg" role="alert"><p class="font-bold">Erreur</p><p>${message}</p></div>`;
        }

        // --- Set Dynamic Copyright Year ---
        document.addEventListener('DOMContentLoaded', () => {
            const copyrightEl = document.getElementById('copyright');
            if (copyrightEl) {
                copyrightEl.innerHTML = `Copyright &copy; ${new Date().getFullYear()} FlexoDiv`;
            }

            // --- Gestion du scénario d'utilisation pliable ---
            const toggleScenario = document.getElementById('toggleScenario');
            const scenarioContent = document.getElementById('scenarioContent');
            const scenarioArrow = document.getElementById('scenarioArrow');

            if (toggleScenario && scenarioContent && scenarioArrow) {
                toggleScenario.addEventListener('click', () => {
                    const isHidden = scenarioContent.classList.contains('hidden');

                    if (isHidden) {
                        scenarioContent.classList.remove('hidden');
                        scenarioArrow.style.transform = 'rotate(180deg)';
                    } else {
                        scenarioContent.classList.add('hidden');
                        scenarioArrow.style.transform = 'rotate(0deg)';
                    }
                });
            }
        });

    </script>
</body>
</html>
