# 03_interaction-firebase.md : Interaction avec Firebase

**Objectif :** Ce document guide l'agent de codage pour l'implémentation des interactions avec les services Firebase configurés.

**Directives pour l'agent de codage :**

1.  **Initialisation Firebase :** Assurez-vous que l'application Firebase est correctement initialisée en utilisant les variables d'environnement définies dans `01_ARCHITECTURE_ET_SETUP/05_gestion-environnement-env.md`. Les identifiants doivent être chargés de manière sécurisée.
2.  **Authentification (Firebase Authentication) :**
    *   Implémentez les flux d'authentification utilisateur (inscription, connexion, déconnexion, réinitialisation de mot de passe) en utilisant le SDK Firebase Auth.
    *   Gérez l'état d'authentification de l'utilisateur de manière globale et réactive (ex: avec un Context React ou un hook `onAuthStateChanged`).
    *   Protégez les routes et les composants nécessitant une authentification.
3.  **Base de données (Firestore) :**
    *   Implémentez les opérations CRUD (Create, Read, Update, Delete) sur Firestore.
    *   Utilisez les requêtes Firestore pour récupérer, filtrer et trier les données.
    *   Mettez en place des écouteurs en temps réel (`onSnapshot`) pour les données dynamiques si nécessaire.
    *   Respectez les règles de sécurité Firestore que nous définirons pour l'accès aux données.
    *   Context7 (`use context7`) sera une ressource précieuse pour les exemples de code Firestore complexes ou pour comprendre des requêtes spécifiques.
4.  **Stockage (Firebase Storage) :**
    *   Implémentez les fonctionnalités de téléchargement et de récupération de fichiers (images, documents, etc.) vers Firebase Storage.
    *   Gérez la progression des téléchargements et les erreurs.
    *   Respectez les règles de sécurité Storage pour les permissions d'accès aux fichiers.
5.  **Gestion des erreurs spécifiques à Firebase :** Gérez les erreurs renvoyées par Firebase de manière significative pour l'utilisateur (ex: codes d'erreur d'authentification).
6.  **Sécurité des clés API :** Confirmez toujours que les clés API Firebase et autres identifiants sensibles sont chargés via les variables d'environnement et ne sont **jamais** exposés côté client de manière non sécurisée (si cela peut être évité ou doit être géré via des fonctions Cloud, cela sera précisé ultérieurement).
7.  **Test de l'application :** Assurez-vous que l'application fonctionne correctement avec les services Firebase configurés et que les interactions sont sécurisées et fonctionnelles.