/* Import des styles personnalisés */
@import './styles/scrollbar-hide.css';
@import './styles/glowing-card.css';
@import './styles/footer-3d.css';
@import './styles/sticky-header.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles de base pour le body et html */
@layer base {
  html, body {
    /* Permettre le scroll sur mobile/tablette tout en masquant les scrollbars */
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    /* Améliorer le scroll sur mobile */
    -webkit-overflow-scrolling: touch;
    /* Empêcher le bounce sur iOS */
    overscroll-behavior-y: none;
  }

  html::-webkit-scrollbar, body::-webkit-scrollbar {
    display: none;
  }

  body {
    @apply bg-brand-dark text-brand-light font-sans antialiased;
    /* Hauteur minimum pour permettre le scroll */
    min-height: 100vh;
  }
}

/* Classes utilitaires personnalisées */
@layer utilities {
  .hide-all-scrollbars {
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
  }
  
  .hide-all-scrollbars::-webkit-scrollbar {
    display: none;
  }

  /* Responsive scroll optimizations */
  @media (max-width: 768px) {
    .mobile-scroll-fix {
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch;
      overscroll-behavior-y: none;
    }
  }

  @media (max-width: 1024px) {
    .tablet-scroll-fix {
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch;
      overscroll-behavior-y: none;
    }
  }
  
  .gemini-gradient {
    background: linear-gradient(90deg, #1A3452 0%, #2190F6 25%, #6689EF 50%, #8D86ED 75%, #AE87F3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .gemini-gradient-bg {
    background: linear-gradient(90deg, #1A3452 0%, #2190F6 25%, #6689EF 50%, #8D86ED 75%, #AE87F3 100%);
  }
  
  .text-shadow-3d {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}

/* Animations personnalisées */
@layer components {
  .logo-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
  }
  
  .slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
  }
  
  .slide-in-top {
    animation: slideInTop 0.8s ease-out forwards;
  }
  
  .slide-in-bottom {
    animation: slideInBottom 0.8s ease-out forwards;
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
