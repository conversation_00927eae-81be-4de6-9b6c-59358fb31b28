# **02_CHECKLIST-DEPLOIEMENT-FIREBASE.MD - DÉPLOIEMENT FIREBASE**

## **OBJECTIF DU DÉPLOIEMENT FIREBASE**

Ce document définit la procédure complète pour déployer et configurer tous les services Firebase (Firestore, Authentication, Functions, Storage, Hosting) nécessaires au bon fonctionnement de l'application. Firebase constitue le backend de notre stack technologique.

---

## **1. PRÉ-REQUIS ET CONFIGURATION**

### **1.1 Installation et Authentification**

**Outils obligatoires :**
```bash
# Installation Firebase CLI
npm install -g firebase-tools

# Vérification de l'installation
firebase --version

# Authentification
firebase login

# Vérification de l'authentification
firebase projects:list
```

### **1.2 Création du Projet Firebase**

**Via la console Firebase :**
1. <PERSON><PERSON> sur https://console.firebase.google.com
2. C<PERSON>r sur "Ajouter un projet"
3. Nommer le projet (ex: `mon-app-prod`)
4. Configurer Google Analytics (recommandé)
5. <PERSON><PERSON><PERSON> le projet

**Configuration initiale obligatoire :**
- [ ] **Authentication** : Activer les méthodes de connexion nécessaires
- [ ] **Firestore** : Créer la base de données en mode test
- [ ] **Storage** : Activer le stockage de fichiers
- [ ] **Hosting** : Activer l'hébergement (si nécessaire)

### **1.3 Initialisation du Projet Local**

**Configuration initiale du projet Firebase :**
```bash
# Navigation vers le projet
cd /path/to/your/project

# Initialisation Firebase
firebase init

# Sélections obligatoires :
# ? Which Firebase features do you want to set up?
#   ◉ Firestore: Configure security rules and indexes files
#   ◉ Functions: Configure a Cloud Functions directory and files
#   ◉ Hosting: Configure files for Firebase Hosting (si nécessaire)
#   ◉ Storage: Configure a security rules file for Cloud Storage
#   ◉ Emulators: Set up local emulators for Firebase products

# ? Please select an option:
#   ◉ Use an existing project
#   → [Sélectionner votre projet Firebase]

# Configuration Firestore
# ? What file should be used for Firestore Rules? → firestore.rules
# ? What file should be used for Firestore indexes? → firestore.indexes.json

# Configuration Functions
# ? What language would you like to use to write Cloud Functions? → TypeScript
# ? Do you want to use ESLint to catch probable bugs and enforce style? → Yes
# ? Do you want to install dependencies with npm now? → Yes

# Configuration Storage
# ? What file should be used for Storage Rules? → storage.rules

# Configuration Emulators
# ? Which Firebase emulators do you want to set up?
#   ◉ Authentication Emulator
#   ◉ Functions Emulator
#   ◉ Firestore Emulator
#   ◉ Storage Emulator
```

### **1.4 Structure des Fichiers Firebase**

**Vérification de la structure créée :**
```
project-root/
├── firebase.json              # Configuration Firebase
├── .firebaserc               # Configuration des projets
├── firestore.rules           # Règles de sécurité Firestore
├── firestore.indexes.json    # Index Firestore
├── storage.rules             # Règles de sécurité Storage
└── functions/                # Cloud Functions
    ├── src/
    │   └── index.ts
    ├── package.json
    └── tsconfig.json
```

---

## **2. CONFIGURATION FIRESTORE**

### **2.1 Règles de Sécurité Firestore**

**Fichier `firestore.rules` obligatoire :**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Fonction utilitaire pour vérifier l'authentification
    function isAuthenticated() {
      return request.auth != null;
    }

    // Fonction pour vérifier la propriété d'un document
    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    // Fonction pour vérifier les rôles admin
    function isAdmin() {
      return isAuthenticated() &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Fonction de validation des données utilisateur
    function validateUserData(data) {
      return data.keys().hasAll(['email', 'displayName']) &&
             data.email is string &&
             data.displayName is string &&
             data.displayName.size() <= 100 &&
             data.email.matches('.*@.*\\..*');
    }

    // Règles pour les utilisateurs
    match /users/{userId} {
      // Lecture : utilisateur propriétaire ou admin
      allow read: if isOwner(userId) || isAdmin();

      // Écriture : utilisateur propriétaire avec validation des données
      allow create: if isOwner(userId) &&
                       validateUserData(request.resource.data);

      allow update: if isOwner(userId) &&
                       validateUserData(request.resource.data) &&
                       // Empêcher la modification du rôle par l'utilisateur
                       (!('role' in request.resource.data) ||
                        request.resource.data.role == resource.data.role);

      allow delete: if isOwner(userId) || isAdmin();
    }

    // Règles pour les données publiques
    match /public/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }

    // Règles par défaut (tout refuser)
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

### **2.2 Index Firestore**

**Fichier `firestore.indexes.json` :**
```json
{
  "indexes": [
    {
      "collectionGroup": "users",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "email",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    }
  ],
  "fieldOverrides": []
}
```

---

## **3. CONFIGURATION STORAGE**

### **3.1 Règles de Sécurité Storage**

**Fichier `storage.rules` obligatoire :**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Fonction utilitaire pour vérifier l'authentification
    function isAuthenticated() {
      return request.auth != null;
    }

    // Fonction pour vérifier la propriété
    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    // Fonction de validation des images
    function isValidImage() {
      return resource.contentType.matches('image/.*') &&
             resource.size < 5 * 1024 * 1024; // 5MB max
    }

    // Images de profil utilisateur
    match /users/{userId}/profile/{fileName} {
      allow read: if true; // Images de profil publiques
      allow write: if isOwner(userId) && isValidImage();
      allow delete: if isOwner(userId);
    }

    // Documents privés utilisateur
    match /users/{userId}/documents/{fileName} {
      allow read, write: if isOwner(userId);
      allow delete: if isOwner(userId);
    }

    // Fichiers publics (lecture seule pour tous)
    match /public/{fileName} {
      allow read: if true;
      allow write: if false; // Géré par les Cloud Functions
    }

    // Règles par défaut (tout refuser)
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
```

---

## **4. CLOUD FUNCTIONS**

### **4.1 Structure des Functions**

**Organisation du dossier `functions/src/` :**
```typescript
// functions/src/index.ts
import { onRequest } from 'firebase-functions/v2/https'
import { onDocumentCreated, onDocumentDeleted } from 'firebase-functions/v2/firestore'
import { setGlobalOptions } from 'firebase-functions/v2'
import * as admin from 'firebase-admin'

// Configuration globale
setGlobalOptions({
  region: 'europe-west1', // Région européenne pour RGPD
  maxInstances: 10
})

// Initialisation Admin SDK
admin.initializeApp()

// Fonction HTTP exemple
export const api = onRequest(
  {
    cors: true,
    maxInstances: 5
  },
  async (request, response) => {
    try {
      // Vérification de l'authentification
      const token = request.headers.authorization?.split('Bearer ')[1]
      if (!token) {
        response.status(401).json({ error: 'Token manquant' })
        return
      }

      const decodedToken = await admin.auth().verifyIdToken(token)
      const uid = decodedToken.uid

      // Logique de l'API
      response.json({
        message: 'API fonctionnelle',
        userId: uid,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Erreur API:', error)
      response.status(500).json({ error: 'Erreur serveur' })
    }
  }
)

// Trigger Firestore - Création d'utilisateur
export const onUserCreated = onDocumentCreated(
  'users/{userId}',
  async (event) => {
    const userId = event.params.userId

    try {
      // Initialiser les données utilisateur
      await admin.firestore()
        .collection('users')
        .doc(userId)
        .update({
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          lastLoginAt: admin.firestore.FieldValue.serverTimestamp(),
          role: 'user' // Rôle par défaut
        })

      console.log(`Utilisateur ${userId} initialisé`)
    } catch (error) {
      console.error('Erreur initialisation utilisateur:', error)
    }
  }
)
```

---

## **5. DÉPLOIEMENT DES SERVICES**

### **5.1 Déploiement Firestore**

**Commandes de déploiement :**
```bash
# Déploiement des règles de sécurité
firebase deploy --only firestore:rules

# Déploiement des index
firebase deploy --only firestore:indexes

# Déploiement complet Firestore
firebase deploy --only firestore
```

### **5.2 Déploiement Storage**

**Commandes de déploiement :**
```bash
# Déploiement des règles Storage
firebase deploy --only storage

# Vérification des règles
firebase emulators:start --only storage
```

### **5.3 Déploiement Functions**

**Commandes de déploiement :**
```bash
# Navigation vers le dossier functions
cd functions

# Installation des dépendances
npm install

# Build TypeScript
npm run build

# Retour à la racine
cd ..

# Déploiement des functions
firebase deploy --only functions

# Déploiement d'une function spécifique
firebase deploy --only functions:api
```

---

## **6. TESTS ET VALIDATION**

### **6.1 Tests avec Emulators**

**Démarrage des émulateurs :**
```bash
# Démarrer tous les émulateurs
firebase emulators:start

# Démarrer des émulateurs spécifiques
firebase emulators:start --only firestore,auth,functions,storage

# Interface web des émulateurs
# http://localhost:4000
```

### **6.2 Tests des Règles de Sécurité**

**Tests automatisés des règles :**
```bash
# Installation des outils de test
npm install -D @firebase/rules-unit-testing

# Exécution des tests
npm run test:rules
```

**Exemple de test de règles :**
```typescript
// tests/firestore.rules.test.ts
import { initializeTestEnvironment, RulesTestEnvironment } from '@firebase/rules-unit-testing'

describe('Firestore Rules', () => {
  let testEnv: RulesTestEnvironment

  beforeAll(async () => {
    testEnv = await initializeTestEnvironment({
      projectId: 'test-project',
      firestore: {
        rules: fs.readFileSync('firestore.rules', 'utf8')
      }
    })
  })

  afterAll(async () => {
    await testEnv.cleanup()
  })

  it('should allow user to read their own data', async () => {
    const alice = testEnv.authenticatedContext('alice')
    const doc = alice.firestore().doc('users/alice')

    await expect(doc.get()).toAllow()
  })

  it('should deny user from reading other user data', async () => {
    const alice = testEnv.authenticatedContext('alice')
    const doc = alice.firestore().doc('users/bob')

    await expect(doc.get()).toDeny()
  })
})
```

---

## **7. MONITORING ET MAINTENANCE**

### **7.1 Surveillance des Services**

**Métriques à surveiller :**
- **Firestore** : Lectures/écritures, latence, erreurs
- **Functions** : Invocations, durée d'exécution, erreurs
- **Storage** : Uploads/downloads, bande passante
- **Authentication** : Connexions, échecs d'authentification

### **7.2 Logs et Debugging**

**Consultation des logs :**
```bash
# Logs des Cloud Functions
firebase functions:log

# Logs d'une function spécifique
firebase functions:log --only api

# Logs en temps réel
firebase functions:log --follow
```

---

**⚠️ IMPORTANT :** La configuration Firebase est critique pour la sécurité et les performances de l'application. Toutes les règles de sécurité DOIVENT être testées rigoureusement avant le déploiement en production. Aucun accès ne doit être accordé sans validation explicite.