 B<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FloraSynth - FlexoDiv Portfolio</title>

    <!-- <PERSON><PERSON><PERSON>-données -->
    <meta name="description" content="FloraSynth - Plateforme créative révolutionnaire avec IA pour la génération de contenu multimédia">
    <meta name="keywords" content="florasynth, IA créative, génération contenu, Next.js, OpenAI, Stability AI">
    <meta name="author" content="Francisco Cisco - FlexoDiv">

    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- DOM INSPECTOR CSS -->
    <link rel="stylesheet" href="../../../DOM-Inspector-Portable/css/dom-inspector-tooltip.css">

    <!-- Styles intégrés -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0f172a;
            color: white;
            overflow-x: hidden;
        }

        /* 🚫 MASQUER LA SCROLLBAR - Demande de Cisco */
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        *::-webkit-scrollbar {
            display: none;
        }

        html, body {
            scrollbar-width: none;
            -ms-overflow-style: none;
            overflow-y: auto;
            overflow-x: hidden;
        }

        html::-webkit-scrollbar, body::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<!-- 🎯 BALISE-STRATEGIQUE: COMPONENT-DOM-INSPECTOR - Système d'inspection DOM intégré -->
<body x-data="domInspector">

    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-slate-900/95 backdrop-blur-sm border-b border-slate-700" x-data="{ mobileMenuOpen: false }">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex-shrink-0">
                    <a href="../../../index.html" class="text-2xl font-bold text-sky-400 hover:text-sky-300 transition-colors">FlexoDiv</a>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="../../../index.html" class="nav-link text-slate-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">Accueil</a>
                        <a href="../../about.html" class="nav-link text-slate-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">À Propos</a>
                        <a href="../../services.html" class="nav-link text-slate-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">Compétences</a>
                        <a href="../../portfolio.html" class="nav-link text-sky-400 bg-slate-800 px-3 py-2 rounded-md text-sm font-medium transition-colors">Portfolio</a>
                        <a href="../../contact.html" class="nav-link text-slate-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">Contact</a>
                    </div>
                </div>
                <div class="md:hidden">
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-slate-400 hover:text-white focus:outline-none focus:text-white">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <div x-show="mobileMenuOpen" x-transition class="md:hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-slate-800 rounded-lg mt-2">
                    <a href="../../../index.html" class="block text-slate-300 hover:text-white px-3 py-2 rounded-md text-base font-medium transition-colors" @click="mobileMenuOpen = false">Accueil</a>
                    <a href="../../about.html" class="block text-slate-300 hover:text-white px-3 py-2 rounded-md text-base font-medium transition-colors" @click="mobileMenuOpen = false">À Propos</a>
                    <a href="../../services.html" class="block text-slate-300 hover:text-white px-3 py-2 rounded-md text-base font-medium transition-colors" @click="mobileMenuOpen = false">Compétences</a>
                    <a href="../../portfolio.html" class="block text-sky-400 px-3 py-2 rounded-md text-base font-medium" @click="mobileMenuOpen = false">Portfolio</a>
                    <a href="../../contact.html" class="block text-slate-300 hover:text-white px-3 py-2 rounded-md text-base font-medium transition-colors" @click="mobileMenuOpen = false">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Spacer pour compenser la navigation fixe -->
    <div class="h-16"></div>

    <!-- Contenu principal -->
    <main class="min-h-screen">

        <!-- Section d'En-tête du Projet -->
        <section class="py-12 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
            <div class="container mx-auto px-4 max-w-7xl">
                <div class="text-center mb-8">
                    <h1 class="text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
                        FloraSynth
                    </h1>
                    <h2 class="text-xl lg:text-2xl text-slate-300 max-w-3xl mx-auto">
                        Plateforme créative révolutionnaire avec IA pour la génération de contenu multimédia et l'assistance créative
                    </h2>
                    <div class="mt-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                            🚧 En cours de développement
                        </span>
                    </div>
                </div>

                <!-- Image Principale -->
                <div class="max-w-4xl mx-auto">
                    <div class="bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg shadow-2xl h-96 flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-6xl mb-4">🎨</div>
                            <h3 class="text-2xl font-bold text-white mb-2">FloraSynth</h3>
                            <p class="text-purple-100">Plateforme créative avec IA</p>
                            <p class="text-purple-200 text-sm mt-2">Aperçu disponible prochainement</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section d'Informations Clés -->
        <section class="py-16 bg-slate-800">
            <div class="container mx-auto px-4 max-w-7xl">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">

                    <!-- Colonne 1: Description du Projet (2/3 de la largeur) -->
                    <div class="md:col-span-2">
                        <h3 class="text-2xl font-bold mb-6 text-white">Le Projet</h3>

                        <div class="prose prose-invert max-w-none">
                            <h4 class="text-xl font-semibold text-purple-400 mb-4">🎨 Vision Créative</h4>
                            <p class="text-slate-300 mb-6">
                                FloraSynth est une plateforme créative révolutionnaire qui combine l'intelligence artificielle avancée avec des outils de création multimédia pour offrir une expérience créative sans précédent. Cette plateforme vise à démocratiser la création de contenu en rendant accessible des technologies d'IA de pointe pour tous les créateurs.
                            </p>

                            <h4 class="text-xl font-semibold text-purple-400 mb-4">🚀 Fonctionnalités Prévues</h4>
                            <ul class="text-slate-300 space-y-3 mb-6">
                                <li><strong>Génération de Contenu IA :</strong> Création automatique de textes, images et vidéos avec OpenAI GPT-4 et Stability AI</li>
                                <li><strong>Analyse Visuelle Avancée :</strong> Intégration Google Vision API pour l'analyse et l'amélioration d'images</li>
                                <li><strong>Évaluation Créative :</strong> Modèles ML personnalisés pour évaluer et optimiser la qualité créative</li>
                                <li><strong>Interface Intuitive :</strong> Design moderne avec React 18, Next.js 14 et animations Framer Motion</li>
                                <li><strong>Collaboration Temps Réel :</strong> Outils de travail collaboratif pour équipes créatives</li>
                                <li><strong>Export Multi-Format :</strong> Support de nombreux formats pour tous types de projets</li>
                            </ul>

                            <h4 class="text-xl font-semibold text-purple-400 mb-4">🛠️ Architecture Technique</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-slate-300 mb-6">
                                <div>
                                    <h5 class="font-semibold text-white mb-2">Frontend</h5>
                                    <ul class="space-y-1">
                                        <li>• React 18 avec TypeScript</li>
                                        <li>• Next.js 14 pour l'optimisation</li>
                                        <li>• Tailwind CSS pour le design</li>
                                        <li>• Framer Motion pour les animations</li>
                                        <li>• Canvas API pour la manipulation graphique</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-white mb-2">Backend & IA</h5>
                                    <ul class="space-y-1">
                                        <li>• Node.js avec Express.js</li>
                                        <li>• OpenAI GPT-4 pour le contenu</li>
                                        <li>• Stability AI pour les images</li>
                                        <li>• Google Vision API</li>
                                        <li>• Modèles ML personnalisés</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="bg-orange-900/30 border border-orange-500/50 rounded-lg p-4 mb-6">
                                <h5 class="text-lg font-semibold text-orange-400 mb-2">🚧 Statut de Développement</h5>
                                <p class="text-slate-300">
                                    Ce projet ambitieux est actuellement en phase de développement actif. L'architecture technique est définie et les premières fonctionnalités sont en cours d'implémentation. Une version beta sera disponible prochainement.
                                </p>
                            </div>

                            <div class="bg-slate-700 rounded-lg p-4 mb-6">
                                <h5 class="text-lg font-semibold text-green-400 mb-2">📊 Objectifs de Performance</h5>
                                <p class="text-slate-300">
                                    <strong>Temps de génération :</strong> &lt; 3 secondes pour le contenu textuel •
                                    <strong>Qualité créative :</strong> Score cible de 9/10 •
                                    <strong>Satisfaction utilisateur :</strong> Objectif de 95%
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Colonne 2: Fiche Technique (1/3 de la largeur) -->
                    <div class="md:col-span-1">
                        <h3 class="text-2xl font-bold mb-6 text-white">Informations</h3>

                        <div class="bg-slate-700 rounded-lg p-6">
                            <div class="space-y-4">
                                <div>
                                    <h4 class="text-sm font-semibold text-purple-400 uppercase tracking-wide mb-2">Rôle</h4>
                                    <p class="text-slate-300">Concepteur & Développeur Principal</p>
                                </div>

                                <div>
                                    <h4 class="text-sm font-semibold text-purple-400 uppercase tracking-wide mb-2">Technologies</h4>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full">React 18</span>
                                        <span class="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full">Next.js 14</span>
                                        <span class="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full">OpenAI GPT-4</span>
                                        <span class="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full">Stability AI</span>
                                        <span class="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full">TypeScript</span>
                                        <span class="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full">PostgreSQL</span>
                                    </div>
                                </div>

                                <div>
                                    <h4 class="text-sm font-semibold text-purple-400 uppercase tracking-wide mb-2">Année</h4>
                                    <p class="text-slate-300">2024-2025</p>
                                </div>

                                <div>
                                    <h4 class="text-sm font-semibold text-purple-400 uppercase tracking-wide mb-2">Statut</h4>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        🚧 En Développement
                                    </span>
                                </div>

                                <div>
                                    <h4 class="text-sm font-semibold text-purple-400 uppercase tracking-wide mb-2">Type</h4>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        🎨 Plateforme Créative
                                    </span>
                                </div>

                                <div class="pt-4">
                                    <a href="https://github.com/cisco-03/florasynth" target="_blank" class="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                        </svg>
                                        Code Source
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section Galerie d'Images -->
        <section class="py-16 bg-slate-700">
            <div class="container mx-auto px-4 max-w-7xl">
                <h2 class="text-3xl font-bold mb-8 text-white text-center">Fonctionnalités Prévues</h2>

                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg overflow-hidden">
                        <div class="h-48 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="text-4xl mb-2">🤖</div>
                                <p class="font-semibold">Génération IA</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg overflow-hidden">
                        <div class="h-48 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="text-4xl mb-2">👁️</div>
                                <p class="font-semibold">Analyse Visuelle</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-green-600 to-blue-600 rounded-lg overflow-hidden">
                        <div class="h-48 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="text-4xl mb-2">⚡</div>
                                <p class="font-semibold">Performance</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-orange-600 to-red-600 rounded-lg overflow-hidden">
                        <div class="h-48 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="text-4xl mb-2">🎨</div>
                                <p class="font-semibold">Interface Créative</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-teal-600 to-green-600 rounded-lg overflow-hidden">
                        <div class="h-48 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="text-4xl mb-2">🤝</div>
                                <p class="font-semibold">Collaboration</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-br from-indigo-600 to-purple-600 rounded-lg overflow-hidden">
                        <div class="h-48 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="text-4xl mb-2">📤</div>
                                <p class="font-semibold">Export Multi-Format</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section de Navigation -->
        <section class="py-12 bg-slate-800">
            <div class="container mx-auto px-4 max-w-7xl">
                <div class="flex justify-between items-center">
                    <a href="portfolio-project-6.html" class="flex items-center text-sky-400 hover:text-sky-300 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Projet Précédent
                    </a>

                    <a href="../../portfolio.html" class="bg-slate-600 hover:bg-slate-500 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300">
                        Retour au Portfolio
                    </a>

                    <a href="portfolio-project-1.html" class="flex items-center text-sky-400 hover:text-sky-300 transition-colors">
                        Projet Suivant
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-slate-900 border-t border-slate-700">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <h3 class="text-2xl font-bold text-sky-400 mb-4">FlexoDiv</h3>
                    <p class="text-slate-300 mb-6 max-w-md">
                        Spécialiste en ingénierie du prompt et développement créatif.
                        Je transforme vos idées en expériences numériques innovantes.
                    </p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Navigation</h4>
                    <ul class="space-y-2">
                        <li><a href="../../../index.html" class="text-slate-300 hover:text-sky-400 transition-colors">Accueil</a></li>
                        <li><a href="../../about.html" class="text-slate-300 hover:text-sky-400 transition-colors">À Propos</a></li>
                        <li><a href="../../services.html" class="text-slate-300 hover:text-sky-400 transition-colors">Compétences</a></li>
                        <li><a href="../../portfolio.html" class="text-slate-300 hover:text-sky-400 transition-colors">Portfolio</a></li>
                        <li><a href="../../contact.html" class="text-slate-300 hover:text-sky-400 transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Compétences</h4>
                    <ul class="space-y-2">
                        <li><span class="text-slate-300">Ingénierie Prompt</span></li>
                        <li><span class="text-slate-300">Développement Créatif</span></li>
                        <li><span class="text-slate-300">Assistant IA Intégré</span></li>
                        <li><span class="text-slate-300">Animations GSAP</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <script src="../../../js/coordinator.js"></script>
    <script src="../../../js/animations.js"></script>
    <script src="../../../js/navigation.js"></script>
    <script src="../../../js/performance.js"></script>
    <script src="../../../DOM-Inspector-Portable/js/dom-inspector-tooltip.js"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>


    <!-- Script Gemini 2 -->
    <script src="../Theme-GEMINI/animations-gemini-2.js"></script>
    
</body>
</html>
