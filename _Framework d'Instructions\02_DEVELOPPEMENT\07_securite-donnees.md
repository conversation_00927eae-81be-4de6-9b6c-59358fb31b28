# **07_SECURITE-DONNEES.MD - SÉCURITÉ ET PROTECTION DES DONNÉES**

## **OBJECTIF DE LA SÉCURITÉ**

Ce document établit les standards de sécurité obligatoires pour protéger les données utilisateur, prévenir les vulnérabilités et assurer la conformité aux réglementations (RGPD, etc.). La sécurité est une priorité absolue et non négociable.

---

## **1. PRINCIPES FONDAMENTAUX DE SÉCURITÉ**

### **1.1 Règles d'Or de la Sécurité**

**Principes inviolables :**
- **Principe du moindre privilège** : Accorder uniquement les permissions nécessaires
- **Défense en profondeur** : Plusieurs couches de sécurité
- **Validation côté client ET serveur** : Ne jamais faire confiance aux données client
- **Chiffrement systématique** : Toutes les données sensibles chiffrées
- **Audit et traçabilité** : Logs de toutes les actions sensibles

### **1.2 Classification des Données**

**Niveaux de sensibilité :**
1. **Publiques** : Données accessibles à tous (contenu marketing, etc.)
2. **Internes** : Données de l'organisation (configurations, logs non sensibles)
3. **Confidentielles** : Données utilisateur (profils, préférences)
4. **Critiques** : Données sensibles (mots de passe, données financières, santé)

**Traitement selon la classification :**
- **Publiques** : Aucune restriction particulière
- **Internes** : Authentification requise
- **Confidentielles** : Chiffrement en transit et au repos
- **Critiques** : Chiffrement renforcé + audit + accès restreint

---

## **2. SÉCURITÉ FRONTEND**

### **2.1 Protection contre XSS (Cross-Site Scripting)**

**Mesures obligatoires :**
```typescript
// Sanitisation des données utilisateur
import DOMPurify from 'dompurify'

// Fonction de sanitisation obligatoire
export const sanitizeHtml = (dirty: string): string => {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: []
  })
}

// Composant pour afficher du HTML utilisateur
interface SafeHtmlProps {
  content: string
  className?: string
}

const SafeHtml: React.FC<SafeHtmlProps> = ({ content, className }) => {
  const sanitizedContent = useMemo(() => sanitizeHtml(content), [content])
  
  return (
    <div 
      className={className}
      dangerouslySetInnerHTML={{ __html: sanitizedContent }}
    />
  )
}

// Validation des inputs
const validateInput = (input: string, type: 'text' | 'email' | 'url'): boolean => {
  const patterns = {
    text: /^[a-zA-Z0-9\s\-_.,!?]+$/,
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
  }
  
  return patterns[type].test(input)
}
```

### **2.2 Content Security Policy (CSP)**

**Configuration CSP obligatoire dans `index.html` :**
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://apis.google.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self' https://api.example.com https://*.firebaseio.com;
  frame-src 'none';
  object-src 'none';
  base-uri 'self';
  form-action 'self';
">
```

### **2.3 Gestion Sécurisée des Tokens**

**Stockage et gestion des tokens d'authentification :**
```typescript
// Service de gestion des tokens
class TokenService {
  private static readonly TOKEN_KEY = 'auth_token'
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token'

  // Stockage sécurisé (httpOnly cookies recommandé en production)
  static setToken(token: string): void {
    // En développement : sessionStorage
    if (process.env.NODE_ENV === 'development') {
      sessionStorage.setItem(this.TOKEN_KEY, token)
    } else {
      // En production : httpOnly cookies via API
      this.setHttpOnlyCookie(token)
    }
  }

  static getToken(): string | null {
    if (process.env.NODE_ENV === 'development') {
      return sessionStorage.getItem(this.TOKEN_KEY)
    } else {
      // En production : récupération via API
      return this.getTokenFromCookie()
    }
  }

  static removeToken(): void {
    if (process.env.NODE_ENV === 'development') {
      sessionStorage.removeItem(this.TOKEN_KEY)
      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY)
    } else {
      this.clearHttpOnlyCookie()
    }
  }

  // Validation du token
  static isTokenValid(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const now = Date.now() / 1000
      return payload.exp > now
    } catch {
      return false
    }
  }

  // Méthodes privées pour la gestion des cookies (à implémenter)
  private static setHttpOnlyCookie(token: string): void {
    // Implémentation via appel API pour définir le cookie httpOnly
  }

  private static getTokenFromCookie(): string | null {
    // Implémentation via appel API pour récupérer le token
    return null
  }

  private static clearHttpOnlyCookie(): void {
    // Implémentation via appel API pour supprimer le cookie
  }
}
```

---

## **3. SÉCURITÉ FIREBASE**

### **3.1 Règles de Sécurité Firestore**

**Template de règles sécurisées :**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Fonction utilitaire pour vérifier l'authentification
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Fonction pour vérifier la propriété d'un document
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    // Fonction pour vérifier les rôles admin
    function isAdmin() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Règles pour les utilisateurs
    match /users/{userId} {
      // Lecture : utilisateur propriétaire ou admin
      allow read: if isOwner(userId) || isAdmin();
      
      // Écriture : utilisateur propriétaire uniquement
      allow write: if isOwner(userId) && 
                      validateUserData(request.resource.data);
    }
    
    // Règles pour les données publiques
    match /public/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Règles pour les données sensibles
    match /sensitive/{document} {
      allow read, write: if isAdmin();
    }
    
    // Fonction de validation des données utilisateur
    function validateUserData(data) {
      return data.keys().hasAll(['email', 'displayName']) &&
             data.email is string &&
             data.displayName is string &&
             data.displayName.size() <= 100;
    }
  }
}
```

### **3.2 Règles de Sécurité Storage**

**Sécurisation du stockage de fichiers :**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Images de profil utilisateur
    match /users/{userId}/profile/{fileName} {
      allow read: if true; // Images de profil publiques
      allow write: if request.auth != null && 
                      request.auth.uid == userId &&
                      isValidImage(resource);
    }
    
    // Documents privés utilisateur
    match /users/{userId}/documents/{fileName} {
      allow read, write: if request.auth != null && 
                            request.auth.uid == userId;
    }
    
    // Fichiers publics (lecture seule pour tous)
    match /public/{fileName} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Validation des images
    function isValidImage(resource) {
      return resource.contentType.matches('image/.*') &&
             resource.size < 5 * 1024 * 1024; // 5MB max
    }
    
    function isAdmin() {
      return request.auth != null &&
             firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

---

## **4. VALIDATION ET SANITISATION**

### **4.1 Schémas de Validation Zod Sécurisés**

**Validation stricte des données :**
```typescript
import { z } from 'zod'

// Schéma de base pour les chaînes sécurisées
const secureString = z.string()
  .min(1, 'Champ requis')
  .max(1000, 'Texte trop long')
  .refine(
    (val) => !/<script|javascript:|data:/i.test(val),
    'Contenu potentiellement dangereux détecté'
  )

// Schéma pour email sécurisé
const secureEmail = z.string()
  .email('Format email invalide')
  .max(254, 'Email trop long')
  .refine(
    (val) => !/[<>\"'&]/.test(val),
    'Caractères non autorisés dans l\'email'
  )

// Schéma pour mot de passe sécurisé
const securePassword = z.string()
  .min(12, 'Le mot de passe doit contenir au moins 12 caractères')
  .max(128, 'Mot de passe trop long')
  .refine(
    (val) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(val),
    'Le mot de passe doit contenir au moins une minuscule, une majuscule, un chiffre et un caractère spécial'
  )

// Schéma pour URL sécurisée
const secureUrl = z.string()
  .url('URL invalide')
  .refine(
    (val) => /^https:\/\//.test(val),
    'Seules les URLs HTTPS sont autorisées'
  )
  .refine(
    (val) => !val.includes('javascript:') && !val.includes('data:'),
    'URL potentiellement dangereuse'
  )

// Schéma utilisateur complet
export const userSchema = z.object({
  email: secureEmail,
  displayName: secureString.max(100),
  bio: secureString.max(500).optional(),
  website: secureUrl.optional(),
  avatar: secureUrl.optional()
})
```

### **4.2 Middleware de Validation**

**Validation automatique des requêtes :**
```typescript
// Hook de validation sécurisée
export const useSecureValidation = <T extends z.ZodType>(schema: T) => {
  const validate = useCallback((data: unknown): z.infer<T> => {
    try {
      // Validation avec Zod
      const validatedData = schema.parse(data)
      
      // Log de sécurité
      logger.info('Data validation successful', {
        dataType: schema._def.typeName,
        timestamp: new Date().toISOString()
      })
      
      return validatedData
    } catch (error) {
      // Log de tentative de données invalides
      logger.warn('Data validation failed', {
        error: error instanceof z.ZodError ? error.errors : error,
        data: JSON.stringify(data),
        timestamp: new Date().toISOString()
      })
      
      throw error
    }
  }, [schema])

  return { validate }
}
```

---

## **5. CHIFFREMENT ET HACHAGE**

### **5.1 Chiffrement des Données Sensibles**

**Utilitaires de chiffrement côté client :**
```typescript
// Service de chiffrement (pour données très sensibles uniquement)
class EncryptionService {
  private static readonly ALGORITHM = 'AES-GCM'
  private static readonly KEY_LENGTH = 256

  // Génération d'une clé de chiffrement
  static async generateKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      {
        name: this.ALGORITHM,
        length: this.KEY_LENGTH
      },
      true,
      ['encrypt', 'decrypt']
    )
  }

  // Chiffrement
  static async encrypt(data: string, key: CryptoKey): Promise<{
    encrypted: ArrayBuffer,
    iv: Uint8Array
  }> {
    const encoder = new TextEncoder()
    const iv = crypto.getRandomValues(new Uint8Array(12))
    
    const encrypted = await crypto.subtle.encrypt(
      {
        name: this.ALGORITHM,
        iv: iv
      },
      key,
      encoder.encode(data)
    )

    return { encrypted, iv }
  }

  // Déchiffrement
  static async decrypt(
    encrypted: ArrayBuffer, 
    key: CryptoKey, 
    iv: Uint8Array
  ): Promise<string> {
    const decrypted = await crypto.subtle.decrypt(
      {
        name: this.ALGORITHM,
        iv: iv
      },
      key,
      encrypted
    )

    const decoder = new TextDecoder()
    return decoder.decode(decrypted)
  }
}
```

### **5.2 Hachage Sécurisé**

**Hachage pour l'intégrité des données :**
```typescript
// Service de hachage
class HashService {
  // Hachage SHA-256
  static async hash(data: string): Promise<string> {
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(data)
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
    
    // Conversion en hexadécimal
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  // Vérification d'intégrité
  static async verifyIntegrity(data: string, expectedHash: string): Promise<boolean> {
    const actualHash = await this.hash(data)
    return actualHash === expectedHash
  }
}
```

---

## **6. CONFORMITÉ RGPD**

### **6.1 Gestion du Consentement**

**Service de gestion des cookies et consentement :**
```typescript
interface ConsentPreferences {
  necessary: boolean      // Toujours true
  analytics: boolean
  marketing: boolean
  personalization: boolean
}

class ConsentService {
  private static readonly CONSENT_KEY = 'user_consent'
  private static readonly CONSENT_VERSION = '1.0'

  static getConsent(): ConsentPreferences | null {
    const stored = localStorage.getItem(this.CONSENT_KEY)
    if (!stored) return null

    try {
      const parsed = JSON.parse(stored)
      if (parsed.version !== this.CONSENT_VERSION) {
        // Version obsolète, redemander le consentement
        this.clearConsent()
        return null
      }
      return parsed.preferences
    } catch {
      return null
    }
  }

  static setConsent(preferences: ConsentPreferences): void {
    const consentData = {
      version: this.CONSENT_VERSION,
      timestamp: new Date().toISOString(),
      preferences
    }

    localStorage.setItem(this.CONSENT_KEY, JSON.stringify(consentData))
    
    // Log pour audit RGPD
    logger.info('User consent updated', {
      preferences,
      timestamp: consentData.timestamp
    })
  }

  static clearConsent(): void {
    localStorage.removeItem(this.CONSENT_KEY)
  }

  static hasValidConsent(): boolean {
    return this.getConsent() !== null
  }
}
```

### **6.2 Droit à l'Oubli**

**Implémentation du droit à l'effacement :**
```typescript
// Service de suppression des données utilisateur
class DataDeletionService {
  static async deleteUserData(userId: string): Promise<void> {
    try {
      // Log de la demande de suppression
      logger.info('User data deletion requested', { userId })

      // Suppression des données Firestore
      await this.deleteFirestoreData(userId)
      
      // Suppression des fichiers Storage
      await this.deleteStorageFiles(userId)
      
      // Anonymisation des logs (garder pour audit mais anonymiser)
      await this.anonymizeLogs(userId)
      
      // Log de confirmation
      logger.info('User data deletion completed', { userId })
      
    } catch (error) {
      logger.error('User data deletion failed', error, { userId })
      throw error
    }
  }

  private static async deleteFirestoreData(userId: string): Promise<void> {
    const batch = db.batch()
    
    // Supprimer le document utilisateur
    batch.delete(db.collection('users').doc(userId))
    
    // Supprimer les sous-collections
    const userCollections = ['preferences', 'activity', 'private_data']
    for (const collection of userCollections) {
      const docs = await db.collection('users').doc(userId)
        .collection(collection).get()
      
      docs.forEach(doc => batch.delete(doc.ref))
    }
    
    await batch.commit()
  }

  private static async deleteStorageFiles(userId: string): Promise<void> {
    const storage = getStorage()
    const userFolder = ref(storage, `users/${userId}`)
    
    // Lister et supprimer tous les fichiers utilisateur
    const fileList = await listAll(userFolder)
    const deletePromises = fileList.items.map(item => deleteObject(item))
    
    await Promise.all(deletePromises)
  }

  private static async anonymizeLogs(userId: string): Promise<void> {
    // Remplacer l'ID utilisateur par un hash anonyme dans les logs
    const anonymousId = await HashService.hash(userId + 'anonymous_salt')
    
    // Implémentation spécifique selon le système de logs utilisé
    logger.info('User logs anonymized', { 
      originalUserId: userId,
      anonymousId 
    })
  }
}
```

---

## **7. AUDIT ET MONITORING DE SÉCURITÉ**

### **7.1 Logs de Sécurité**

**Événements à logger obligatoirement :**
```typescript
// Service d'audit de sécurité
class SecurityAuditService {
  // Tentative de connexion
  static logLoginAttempt(email: string, success: boolean, ip?: string): void {
    logger.info('Login attempt', {
      email: this.hashEmail(email),
      success,
      ip: this.hashIP(ip),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    })
  }

  // Accès aux données sensibles
  static logSensitiveDataAccess(userId: string, dataType: string): void {
    logger.info('Sensitive data access', {
      userId: this.hashUserId(userId),
      dataType,
      timestamp: new Date().toISOString()
    })
  }

  // Modification de données
  static logDataModification(userId: string, collection: string, action: string): void {
    logger.info('Data modification', {
      userId: this.hashUserId(userId),
      collection,
      action,
      timestamp: new Date().toISOString()
    })
  }

  // Tentative d'accès non autorisé
  static logUnauthorizedAccess(userId: string, resource: string): void {
    logger.warn('Unauthorized access attempt', {
      userId: this.hashUserId(userId),
      resource,
      timestamp: new Date().toISOString()
    })
  }

  // Méthodes de hachage pour l'anonymisation
  private static hashEmail(email: string): string {
    return HashService.hash(email + 'email_salt')
  }

  private static hashUserId(userId: string): string {
    return HashService.hash(userId + 'user_salt')
  }

  private static hashIP(ip?: string): string {
    return ip ? HashService.hash(ip + 'ip_salt') : 'unknown'
  }
}
```

---

## **8. TESTS DE SÉCURITÉ**

### **8.1 Tests de Validation**

**Tests de sécurité obligatoires :**
```typescript
describe('Security Validation', () => {
  describe('Input Sanitization', () => {
    it('should reject XSS attempts', () => {
      const maliciousInput = '<script>alert("xss")</script>'
      const sanitized = sanitizeHtml(maliciousInput)
      expect(sanitized).not.toContain('<script>')
    })

    it('should validate email format securely', () => {
      const maliciousEmail = '<EMAIL><script>alert("xss")</script>'
      expect(validateInput(maliciousEmail, 'email')).toBe(false)
    })
  })

  describe('Token Security', () => {
    it('should detect expired tokens', () => {
      const expiredToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2MDk0NTkyMDB9.invalid'
      expect(TokenService.isTokenValid(expiredToken)).toBe(false)
    })
  })

  describe('Data Encryption', () => {
    it('should encrypt and decrypt data correctly', async () => {
      const originalData = 'sensitive information'
      const key = await EncryptionService.generateKey()
      
      const { encrypted, iv } = await EncryptionService.encrypt(originalData, key)
      const decrypted = await EncryptionService.decrypt(encrypted, key, iv)
      
      expect(decrypted).toBe(originalData)
    })
  })
})
```

---

**⚠️ CRITIQUE :** La sécurité n'est pas optionnelle. Tous les patterns et mesures définis dans ce document DOIVENT être implémentés. Toute dérogation doit être explicitement validée par Cisco et documentée avec justification technique.
