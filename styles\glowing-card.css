@property --angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}
:root {
  --gemini-blue-light: #2190F6;
  --gemini-blue-purple: #6689EF;
  --gemini-purple: rgb(77, 70, 175);
  --gemini-salmon: rgb(235, 73, 114);
}
/* SUPPRESSION DES STYLES GLOBAUX QUI CAUSAIENT LES HALOS INDÉSIRABLES */
/* <PERSON><PERSON> styles ne s'appliquent plus qu'aux éléments avec la classe .glowing-card */
.glowing-card {
  margin: 0 auto; padding: 2.5em; width: 100%; max-width: 600px;
  background: #1c1f2b; border-radius: 12px; position: relative; text-align: left;
}
.glowing-card::before,
.glowing-card::after {
  content: ''; position: absolute; top: 50%; left: 50%;
  transform: translate(-50%, -50%); z-index: -1;
  width: 100%; height: 100%; border-radius: 12px;
}
.glowing-card::before,
.glowing-card::after {
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
}
.glowing-card::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
}
.glowing-card::after {
  filter: brightness(2.5);
}
.glowing-card::before,
.glowing-card::after {
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}
.glowing-card:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}
.glowing-card:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}
@keyframes spin {
  from { --angle: 0deg; }
  to { --angle: 360deg; }
}
.glowing-card h2 {
  color: white; margin-bottom: 1rem; border-bottom: 1px solid #333;
  padding-bottom: 0.5rem; font-size: 1.25rem;
}
.glowing-card ul { list-style-position: inside; padding-left: 0; }
.glowing-card li { margin-bottom: 0.75rem; line-height: 1.6; }
.glowing-card li::marker { color: var(--gemini-blue-purple); }
