import React, { useRef, useEffect } from 'react';

interface Node {
    x: number;
    y: number;
    size: number;
    opacity: number;
    pulsePhase: number;
    pulseSpeed: number;
    color: string;
    connections: number[];
    activity: number;
}

interface Impulse {
    fromNode: number;
    toNode: number;
    progress: number;
    speed: number;
    size: number;
    opacity: number;
    color: string;
    life: number;
    maxLife: number;
}

const NeuralNetworkBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    // Fonction utilitaire pour convertir hex en RGB
    const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    };

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let animationId: number;
        let nodes: Node[] = [];
        let impulses: Impulse[] = [];
        const nodeCount = 50;
        const maxConnections = 4;
        const maxImpulses = 20;
        let mouseX = 0;
        let mouseY = 0;
        const mouseInfluence = 120;

        // Couleurs du gradient Gemini
        const geminiColors = [
            '#1A3452', '#2190F6', '#6689EF', '#8D86ED', '#AE87F3'
        ];

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            nodes = [];
            impulses = [];

            // Créer les nœuds
            for (let i = 0; i < nodeCount; i++) {
                const node: Node = {
                    x: Math.random() * (canvas.width - 100) + 50,
                    y: Math.random() * (canvas.height - 100) + 50,
                    size: Math.random() * 8 + 4,
                    opacity: Math.random() * 0.8 + 0.2,
                    pulsePhase: Math.random() * Math.PI * 2,
                    pulseSpeed: Math.random() * 0.02 + 0.01,
                    color: geminiColors[Math.floor(Math.random() * geminiColors.length)],
                    connections: [],
                    activity: Math.random()
                };
                nodes.push(node);
            }

            // Créer les connexions entre nœuds proches
            nodes.forEach((node, index) => {
                const distances = nodes.map((otherNode, otherIndex) => ({
                    index: otherIndex,
                    distance: Math.sqrt(
                        Math.pow(node.x - otherNode.x, 2) + 
                        Math.pow(node.y - otherNode.y, 2)
                    )
                })).filter(d => d.index !== index).sort((a, b) => a.distance - b.distance);

                // Connecter aux nœuds les plus proches
                const connectionCount = Math.min(maxConnections, Math.floor(Math.random() * 3) + 1);
                for (let i = 0; i < connectionCount && i < distances.length; i++) {
                    if (distances[i].distance < 200) {
                        node.connections.push(distances[i].index);
                    }
                }
            });
        };

        const createImpulse = (fromIndex: number, toIndex: number) => {
            const impulse: Impulse = {
                fromNode: fromIndex,
                toNode: toIndex,
                progress: 0,
                speed: Math.random() * 0.02 + 0.01,
                size: Math.random() * 4 + 2,
                opacity: Math.random() * 0.8 + 0.4,
                color: nodes[fromIndex].color,
                life: 0,
                maxLife: 100
            };
            return impulse;
        };

        const updateNode = (node: Node, time: number) => {
            // Influence de la souris sur l'activité des nœuds
            const dx = mouseX - node.x;
            const dy = mouseY - node.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < mouseInfluence) {
                const influence = (mouseInfluence - distance) / mouseInfluence;
                node.activity = Math.min(1, node.activity + influence * 0.1);
            }

            // Pulsation plus visible
            const pulse = Math.sin(time * 0.001 * node.pulseSpeed + node.pulsePhase) * 0.4 + 0.6;
            node.opacity = pulse;

            // Activité neuronale plus dynamique
            node.activity += (Math.random() - 0.5) * 0.05;
            node.activity = Math.max(0, Math.min(1, node.activity));

            // Décroissance naturelle de l'activité
            node.activity *= 0.995;
        };

        const updateImpulse = (impulse: Impulse) => {
            impulse.progress += impulse.speed;
            impulse.life++;
            
            // Fade out en fin de vie
            const lifeRatio = impulse.life / impulse.maxLife;
            if (lifeRatio > 0.8) {
                impulse.opacity *= 0.95;
            }
            
            // Augmenter l'activité du nœud de destination quand l'impulsion arrive
            if (impulse.progress >= 0.9 && impulse.progress < 1) {
                nodes[impulse.toNode].activity = Math.min(1, nodes[impulse.toNode].activity + 0.3);
            }
            
            return impulse.progress < 1 && impulse.opacity > 0.01;
        };

        const drawNode = (node: Node, time: number) => {
            // Effet de lueur basé sur l'activité
            const activityGlow = node.activity * 20;
            const gradient = ctx.createRadialGradient(
                node.x, node.y, 0,
                node.x, node.y, node.size + activityGlow
            );

            // Correction: utiliser rgba() au lieu de concaténer hex + alpha
            const baseColor = node.color;
            const rgb = hexToRgb(baseColor);
            if (rgb) {
                gradient.addColorStop(0, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${node.opacity})`);
                gradient.addColorStop(0.3, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${node.opacity * 0.5})`);
                gradient.addColorStop(1, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0)`);
            }

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.size + activityGlow, 0, Math.PI * 2);
            ctx.fill();

            // Nœud principal
            if (rgb) {
                ctx.fillStyle = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${node.opacity + node.activity * 0.5})`;
            }
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.size, 0, Math.PI * 2);
            ctx.fill();

            // Anneau d'activité
            if (node.activity > 0.5 && rgb) {
                ctx.strokeStyle = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${node.activity})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(node.x, node.y, node.size + 5, 0, Math.PI * 2);
                ctx.stroke();
            }
        };

        const drawConnection = (fromNode: Node, toNode: Node) => {
            const opacity = Math.min(fromNode.opacity, toNode.opacity) * 0.3;
            const gradient = ctx.createLinearGradient(
                fromNode.x, fromNode.y,
                toNode.x, toNode.y
            );

            // Correction: utiliser rgba() au lieu de concaténer hex + alpha
            const fromRgb = hexToRgb(fromNode.color);
            const toRgb = hexToRgb(toNode.color);

            if (fromRgb && toRgb) {
                gradient.addColorStop(0, `rgba(${fromRgb.r}, ${fromRgb.g}, ${fromRgb.b}, ${opacity})`);
                gradient.addColorStop(1, `rgba(${toRgb.r}, ${toRgb.g}, ${toRgb.b}, ${opacity})`);
            }

            ctx.strokeStyle = gradient;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(fromNode.x, fromNode.y);
            ctx.lineTo(toNode.x, toNode.y);
            ctx.stroke();
        };

        const drawImpulse = (impulse: Impulse) => {
            const fromNode = nodes[impulse.fromNode];
            const toNode = nodes[impulse.toNode];

            if (!fromNode || !toNode) return;

            // Position interpolée
            const x = fromNode.x + (toNode.x - fromNode.x) * impulse.progress;
            const y = fromNode.y + (toNode.y - fromNode.y) * impulse.progress;

            // Effet de lueur
            const gradient = ctx.createRadialGradient(
                x, y, 0,
                x, y, impulse.size * 4
            );

            // Correction: utiliser rgba() au lieu de concaténer hex + alpha
            const rgb = hexToRgb(impulse.color);
            if (rgb) {
                gradient.addColorStop(0, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${impulse.opacity})`);
                gradient.addColorStop(0.5, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${impulse.opacity * 0.5})`);
                gradient.addColorStop(1, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0)`);
            }

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, impulse.size * 4, 0, Math.PI * 2);
            ctx.fill();

            // Impulsion principale
            ctx.fillStyle = impulse.color;
            ctx.shadowColor = impulse.color;
            ctx.shadowBlur = 15;
            ctx.beginPath();
            ctx.arc(x, y, impulse.size, 0, Math.PI * 2);
            ctx.fill();
            ctx.shadowBlur = 0;

            // Traînée
            const trailLength = 20;
            const trailX = x - (toNode.x - fromNode.x) * 0.1;
            const trailY = y - (toNode.y - fromNode.y) * 0.1;

            if (rgb) {
                ctx.strokeStyle = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${impulse.opacity * 0.5})`;
            }
            ctx.lineWidth = impulse.size;
            ctx.beginPath();
            ctx.moveTo(trailX, trailY);
            ctx.lineTo(x, y);
            ctx.stroke();
        };

        const animate = (time: number) => {
            // Fond noir avec légère transparence
            ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Mettre à jour les nœuds
            nodes.forEach(node => updateNode(node, time));

            // Dessiner les connexions
            nodes.forEach((node, index) => {
                node.connections.forEach(connectionIndex => {
                    if (connectionIndex < nodes.length) {
                        drawConnection(node, nodes[connectionIndex]);
                    }
                });
            });

            // Activer automatiquement des nœuds pour maintenir l'activité constante
            if (Math.random() < 0.3) {
                const randomNode = nodes[Math.floor(Math.random() * nodes.length)];
                randomNode.activity = Math.min(1, randomNode.activity + 0.8);
            }

            // Créer de nouvelles impulsions très fréquemment
            if (impulses.length < maxImpulses && Math.random() < 0.4) {
                const activeNodes = nodes.filter(node => node.activity > 0.1);
                if (activeNodes.length === 0) {
                    // Si aucun nœud n'est actif, en activer un
                    const randomNode = nodes[Math.floor(Math.random() * nodes.length)];
                    randomNode.activity = 1;
                    activeNodes.push(randomNode);
                }

                if (activeNodes.length > 0) {
                    const fromNode = activeNodes[Math.floor(Math.random() * activeNodes.length)];
                    const fromIndex = nodes.indexOf(fromNode);

                    if (fromNode.connections.length > 0) {
                        const toIndex = fromNode.connections[Math.floor(Math.random() * fromNode.connections.length)];
                        impulses.push(createImpulse(fromIndex, toIndex));
                    }
                }
            }

            // Mettre à jour et dessiner les impulsions
            impulses = impulses.filter(impulse => {
                const alive = updateImpulse(impulse);
                if (alive) {
                    drawImpulse(impulse);
                }
                return alive;
            });

            // Dessiner les nœuds
            nodes.forEach(node => drawNode(node, time));

            animationId = requestAnimationFrame(animate);
        };

        initialize();
        animate(0);

        const handleResize = () => {
            initialize();
        };

        // Activer un nœud au clic
        const handleClick = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // Trouver le nœud le plus proche
            let closestNode = null;
            let closestDistance = Infinity;

            nodes.forEach(node => {
                const distance = Math.sqrt(Math.pow(x - node.x, 2) + Math.pow(y - node.y, 2));
                if (distance < closestDistance && distance < 50) {
                    closestDistance = distance;
                    closestNode = node;
                }
            });

            if (closestNode) {
                closestNode.activity = 1;
            }
        };

        // Suivre la position de la souris
        const handleMouseMove = (event: MouseEvent) => {
            mouseX = event.clientX;
            mouseY = event.clientY;
        };

        window.addEventListener('resize', handleResize);
        canvas.addEventListener('click', handleClick);
        window.addEventListener('mousemove', handleMouseMove);

        return () => {
            cancelAnimationFrame(animationId);
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('click', handleClick);
            window.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas ref={canvasRef} />
        </div>
    );
};

export default NeuralNetworkBackground;
