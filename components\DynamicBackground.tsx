import React from 'react';
import { useLocation } from 'react-router-dom';
import MatrixBackground from './MatrixBackground';
import SphereBackground from './SphereBackground';
import ConstellationBackground from './ConstellationBackground';
import CodeParticlesBackground from './CodeParticlesBackground';
import HexGridBackground from './HexGridBackground';
import WaveBackground from './WaveBackground';
import NeuralNetworkBackground from './NeuralNetworkBackground';

const DynamicBackground: React.FC = () => {
    const location = useLocation();

    // Animation Matrix pour la page d'accueil
    if (location.pathname === '/' || location.pathname === '') {
        return <MatrixBackground />;
    }

    // Animation Constellation pour la page À Propos
    if (location.pathname === '/a-propos') {
        return <ConstellationBackground />;
    }

    // Animation Constellation pour la page Compétences
    if (location.pathname === '/competences') {
        return <ConstellationBackground />;
    }

    // Animation Constellation pour la page Portfolio
    if (location.pathname === '/portfolio') {
        return <ConstellationBackground />;
    }

    // Animation Constellation pour la page Assistance
    if (location.pathname === '/assistance') {
        return <ConstellationBackground />;
    }

    // Animation Constellation pour la page Contact
    if (location.pathname === '/contact') {
        return <ConstellationBackground />;
    }

    // Fond simple pour les autres pages (projets, packs d'assistance, etc.)
    return (
        <div
            className="fixed top-0 left-0 w-full h-full -z-10"
            style={{ background: 'linear-gradient(135deg, #1B1C1D 0%, #282A2C 100%)' }}
        />
    );
};

export default DynamicBackground;
