import React, { useRef, useEffect } from 'react';

interface Wave {
    x: number;
    y: number;
    radius: number;
    maxRadius: number;
    speed: number;
    opacity: number;
    color: string;
    life: number;
    maxLife: number;
}

interface WaveParticle {
    x: number;
    y: number;
    angle: number;
    distance: number;
    speed: number;
    size: number;
    opacity: number;
    color: string;
    waveIndex: number;
    life: number;
    maxLife: number;
}

const WaveBackground: React.FC = () => {
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        let animationId: number;
        let waves: Wave[] = [];
        let particles: WaveParticle[] = [];
        const maxWaves = 3; // Réduit de 8 à 3
        const maxParticles = 30; // Réduit de 100 à 30
        let mouseX = 0;
        let mouseY = 0;

        // Couleurs du gradient Gemini
        const geminiColors = [
            '#1A3452', '#2190F6', '#6689EF', '#8D86ED', '#AE87F3'
        ];

        const initialize = () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            waves = [];
            particles = [];
        };

        const createWave = (x?: number, y?: number) => {
            const wave: Wave = {
                x: x || Math.random() * canvas.width,
                y: y || Math.random() * canvas.height,
                radius: 0,
                maxRadius: Math.random() * 200 + 150, // Réduit la taille
                speed: Math.random() * 0.8 + 0.3, // Beaucoup plus lent
                opacity: Math.random() * 0.4 + 0.1, // Réduit l'opacité
                color: geminiColors[Math.floor(Math.random() * geminiColors.length)],
                life: 0,
                maxLife: 500 // Augmenté pour durée plus longue
            };
            return wave;
        };

        const createWaveParticle = (wave: Wave, waveIndex: number) => {
            const angle = Math.random() * Math.PI * 2;
            const particle: WaveParticle = {
                x: wave.x,
                y: wave.y,
                angle,
                distance: wave.radius,
                speed: Math.random() * 0.5 + 0.2,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.8 + 0.2,
                color: wave.color,
                waveIndex,
                life: 0,
                maxLife: Math.random() * 200 + 100
            };
            return particle;
        };

        const updateWave = (wave: Wave) => {
            wave.radius += wave.speed;
            wave.life++;
            
            // Fade out progressif
            const lifeRatio = wave.life / wave.maxLife;
            if (lifeRatio > 0.7) {
                wave.opacity *= 0.98;
            }
            
            return wave.radius < wave.maxRadius && wave.opacity > 0.01;
        };

        const updateWaveParticle = (particle: WaveParticle) => {
            particle.life++;
            particle.distance += particle.speed;
            
            // Position sur le cercle
            particle.x = waves[particle.waveIndex]?.x + Math.cos(particle.angle) * particle.distance || particle.x;
            particle.y = waves[particle.waveIndex]?.y + Math.sin(particle.angle) * particle.distance || particle.y;
            
            // Oscillation de l'angle pour un mouvement plus organique
            particle.angle += Math.sin(particle.life * 0.02) * 0.01;
            
            // Fade out
            const lifeRatio = particle.life / particle.maxLife;
            if (lifeRatio > 0.8) {
                particle.opacity *= 0.95;
            }
            
            return particle.life < particle.maxLife && particle.opacity > 0.01 && 
                   particle.distance < 400;
        };

        const drawWave = (wave: Wave) => {
            // Onde principale
            // Correction couleur hex + alpha
            const baseColor = wave.color.length === 7 ? wave.color : '#2190F6';
            const alphaHex = Math.floor(wave.opacity * 255).toString(16).padStart(2, '0');
            ctx.strokeStyle = baseColor + alphaHex;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(wave.x, wave.y, wave.radius, 0, Math.PI * 2);
            ctx.stroke();
            
            // Effet de lueur avec vérification des rayons
            const innerRadius = Math.max(0, wave.radius - 10);
            const outerRadius = Math.max(innerRadius + 1, wave.radius + 10);
            // Sécurisation des valeurs pour éviter NaN
            if (isFinite(wave.x) && isFinite(wave.y) && isFinite(innerRadius) && isFinite(outerRadius)) {
                const gradient = ctx.createRadialGradient(
                    wave.x, wave.y, innerRadius,
                    wave.x, wave.y, outerRadius
                );
                gradient.addColorStop(0, baseColor + '00');
                gradient.addColorStop(0.5, baseColor + alphaHex);
                gradient.addColorStop(1, baseColor + '00');
                ctx.strokeStyle = gradient;
                ctx.lineWidth = 8;
                ctx.beginPath();
                ctx.arc(wave.x, wave.y, wave.radius, 0, Math.PI * 2);
                ctx.stroke();
            }
            // Onde secondaire plus subtile avec vérification du rayon
            if (wave.radius > 20) {
                const secondaryRadius = Math.max(1, wave.radius - 15);
                const alphaHex2 = Math.round(wave.opacity * 128).toString(16).padStart(2, '0');
                ctx.strokeStyle = baseColor + alphaHex2;
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(wave.x, wave.y, secondaryRadius, 0, Math.PI * 2);
                ctx.stroke();
            }
        };

        const drawWaveParticle = (particle: WaveParticle) => {
            // Correction couleur hex + alpha
            const baseColor = particle.color.length === 7 ? particle.color : '#2190F6';
            const alphaHex = Math.floor(particle.opacity * 255).toString(16).padStart(2, '0');
            const alphaHex2 = Math.round(particle.opacity * 128).toString(16).padStart(2, '0');
            if (isFinite(particle.x) && isFinite(particle.y) && isFinite(particle.size)) {
                const gradient = ctx.createRadialGradient(
                    particle.x, particle.y, 0,
                    particle.x, particle.y, particle.size * 3
                );
                gradient.addColorStop(0, baseColor + alphaHex);
                gradient.addColorStop(0.5, baseColor + alphaHex2);
                gradient.addColorStop(1, baseColor + '00');
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size * 3, 0, Math.PI * 2);
                ctx.fill();
            }
            // Point central
            ctx.fillStyle = baseColor + alphaHex;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
        };

        const animate = (time: number) => {
            // Fond noir avec légère transparence pour l'effet de traînée
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Créer de nouvelles ondes beaucoup moins fréquemment
            if (waves.length < maxWaves && Math.random() < 0.02) {
                // Parfois créer une onde près de la souris pour l'interactivité
                if (mouseX > 0 && mouseY > 0 && Math.random() < 0.3) {
                    const offsetX = (Math.random() - 0.5) * 100;
                    const offsetY = (Math.random() - 0.5) * 100;
                    waves.push(createWave(mouseX + offsetX, mouseY + offsetY));
                } else {
                    waves.push(createWave());
                }
            }

            // Créer une onde initiale si aucune n'existe
            if (waves.length === 0) {
                waves.push(createWave(canvas.width / 2, canvas.height / 2));
            }

            // Mettre à jour et dessiner les ondes
            waves = waves.filter((wave, index) => {
                const alive = updateWave(wave);
                if (alive) {
                    drawWave(wave);
                    
                    // Créer beaucoup moins de particules sur l'onde
                    if (Math.random() < 0.03 && particles.length < maxParticles) {
                        particles.push(createWaveParticle(wave, index));
                    }
                }
                return alive;
            });

            // Mettre à jour et dessiner les particules
            particles = particles.filter(particle => {
                const alive = updateWaveParticle(particle);
                if (alive) {
                    drawWaveParticle(particle);
                }
                return alive;
            });

            // Effet d'impulsion centrale très occasionnel et réduit
            if (Math.random() < 0.001) {
                const centerX = canvas.width / 2 + (Math.random() - 0.5) * 100;
                const centerY = canvas.height / 2 + (Math.random() - 0.5) * 100;
                waves.push(createWave(centerX, centerY));

                // Créer moins de particules autour du centre
                for (let i = 0; i < 4; i++) {
                    const angle = (i / 4) * Math.PI * 2;
                    const particle: WaveParticle = {
                        x: centerX,
                        y: centerY,
                        angle,
                        distance: 0,
                        speed: Math.random() * 0.5 + 0.2, // Plus lent
                        size: Math.random() * 3 + 1, // Plus petit
                        opacity: 0.5, // Moins visible
                        color: geminiColors[Math.floor(Math.random() * geminiColors.length)],
                        waveIndex: waves.length - 1,
                        life: 0,
                        maxLife: 300
                    };
                    particles.push(particle);
                }
            }

            animationId = requestAnimationFrame(animate);
        };

        initialize();
        animate(0);

        const handleResize = () => {
            initialize();
        };

        // Créer une onde au clic de souris
        const handleClick = (event: MouseEvent) => {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            waves.push(createWave(x, y));
        };

        // Suivre la position de la souris
        const handleMouseMove = (event: MouseEvent) => {
            mouseX = event.clientX;
            mouseY = event.clientY;
        };

        window.addEventListener('resize', handleResize);
        canvas.addEventListener('click', handleClick);
        window.addEventListener('mousemove', handleMouseMove);

        return () => {
            cancelAnimationFrame(animationId);
            window.removeEventListener('resize', handleResize);
            canvas.removeEventListener('click', handleClick);
            window.removeEventListener('mousemove', handleMouseMove);
        };
    }, []);

    return (
        <div className="fixed top-0 left-0 w-full h-full -z-10" style={{ background: '#000' }}>
            <canvas ref={canvasRef} />
        </div>
    );
};

export default WaveBackground;
