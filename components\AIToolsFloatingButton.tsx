import React, { useState } from 'react';
import AIToolsPanel from './AIToolsPanel';

const AIToolsFloatingButton: React.FC = () => {
    const [isPanelOpen, setIsPanelOpen] = useState(false);

    return (
        <>
            {/* Bouton flottant avec effet néon - repositionné pour ne pas cacher le scroll */}
            <button
                onClick={() => setIsPanelOpen(true)}
                className="ai-tools-floating-button fixed bottom-8 right-24 z-40 group"
                style={{
                    background: 'linear-gradient(135deg, #1A3452 0%, #2190F6 25%, #6689EF 50%, #8D86ED 75%, #AE87F3 100%)',
                    borderRadius: '50%',
                    width: '64px',
                    height: '64px',
                    border: '2px solid #D65E65',
                    boxShadow: '0 0 20px rgba(214, 94, 101, 0.5), 0 0 40px rgba(214, 94, 101, 0.3)',
                    transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.1)';
                    e.currentTarget.style.boxShadow = '0 0 30px rgba(214, 94, 101, 0.8), 0 0 60px rgba(214, 94, 101, 0.5)';
                }}
                onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                    e.currentTarget.style.boxShadow = '0 0 20px rgba(214, 94, 101, 0.5), 0 0 40px rgba(214, 94, 101, 0.3)';
                }}
            >
                <div className="flex items-center justify-center h-full text-white text-2xl">
                    🌟
                </div>
                
                {/* Tooltip au survol */}
                <div className="absolute bottom-full right-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                    <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap border border-gray-700">
                        Outils IA & LLM
                        <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                    </div>
                </div>
            </button>

            {/* Animation de pulsation - SUPPRESSION DU STYLE JSX QUI CAUSAIT L'ERREUR */}
            <style dangerouslySetInnerHTML={{
                __html: `
                    @keyframes pulse-glow {
                        0%, 100% {
                            box-shadow: 0 0 20px rgba(214, 94, 101, 0.5), 0 0 40px rgba(214, 94, 101, 0.3);
                        }
                        50% {
                            box-shadow: 0 0 25px rgba(214, 94, 101, 0.7), 0 0 50px rgba(214, 94, 101, 0.4);
                        }
                    }

                    .ai-tools-floating-button {
                        animation: pulse-glow 2s ease-in-out infinite;
                    }
                `
            }} />

            {/* Panneau des outils */}
            <AIToolsPanel 
                isVisible={isPanelOpen}
                onClose={() => setIsPanelOpen(false)}
            />
        </>
    );
};

export default AIToolsFloatingButton;
