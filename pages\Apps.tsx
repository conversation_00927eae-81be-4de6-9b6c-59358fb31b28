import React from 'react';
import { Link } from 'react-router-dom';
import '../css/glowing-effect.css';
import ConstellationBackground from '../components/ConstellationBackground';

interface App {
    title: string;
    description: string;
    imageUrl: string;
    tags: string[];
    link: string;
    status: 'active' | 'coming-soon';
    icon: string;
}

const appsData: App[] = [
    {
        title: 'Info-Société Pro',
        description: 'Recherchez des informations complètes sur n\'importe quelle entreprise et générez automatiquement des accroches personnalisées avec l\'IA Gemini.',
        imageUrl: '/assets/Apps/info-societe-pro-preview.jpg',
        tags: ['Recherche', 'IA Gemini', 'Prospection'],
        link: '/info-societe-pro',
        status: 'active',
        icon: 'clipboard'
    },
    {
        title: 'Prototypage à venir',
        description: 'Une nouvelle application innovante en cours de développement. Restez connecté pour découvrir nos prochains outils IA.',
        imageUrl: '/assets/Apps/coming-soon.jpg',
        tags: ['B<PERSON>ôt'],
        link: '#',
        status: 'coming-soon',
        icon: 'user'
    },
    {
        title: 'Prototypage en cours',
        description: 'Application en phase de conception avec intégration IA avancée. Sortie prévue prochainement.',
        imageUrl: '/assets/Apps/coming-soon.jpg',
        tags: ['En développement'],
        link: '#',
        status: 'coming-soon',
        icon: 'mail'
    },
    {
        title: 'Prototypage à venir',
        description: 'Nouvel outil professionnel avec intelligence artificielle en préparation. Restez à l\'écoute pour plus d\'informations.',
        imageUrl: '/assets/Apps/coming-soon.jpg',
        tags: ['Bientôt'],
        link: '#',
        status: 'coming-soon',
        icon: 'code'
    },
    {
        title: 'Prototypage en cours',
        description: 'Solution innovante en phase de test avec fonctionnalités IA avancées. Lancement prévu dans les prochaines semaines.',
        imageUrl: '/assets/Apps/coming-soon.jpg',
        tags: ['En développement'],
        link: '#',
        status: 'coming-soon',
        icon: 'mic'
    },
    {
        title: 'Prototypage à venir',
        description: 'Application professionnelle en conception avec intégration Gemini. Restez connecté pour découvrir cette nouveauté.',
        imageUrl: '/assets/Apps/coming-soon.jpg',
        tags: ['Bientôt'],
        link: '#',
        status: 'coming-soon',
        icon: 'briefcase'
    },
    {
        title: 'Prototypage en cours',
        description: 'Outil créatif avec IA en développement actif. Sortie imminente avec des fonctionnalités révolutionnaires.',
        imageUrl: '/assets/Apps/coming-soon.jpg',
        tags: ['En développement'],
        link: '#',
        status: 'coming-soon',
        icon: 'share'
    }
];

const Apps: React.FC = () => {
    const getIcon = (iconName: string) => {
        switch (iconName) {
            case 'clipboard':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                    </svg>
                );
            case 'user':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                );
            case 'mail':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                );
            case 'code':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="16,18 22,12 16,6"></polyline>
                        <polyline points="8,6 2,12 8,18"></polyline>
                    </svg>
                );
            case 'mic':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                        <line x1="12" y1="19" x2="12" y2="23"></line>
                        <line x1="8" y1="23" x2="16" y2="23"></line>
                    </svg>
                );
            case 'briefcase':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                        <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                    </svg>
                );
            case 'share':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="18" cy="5" r="3"></circle>
                        <circle cx="6" cy="12" r="3"></circle>
                        <circle cx="18" cy="19" r="3"></circle>
                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                    </svg>
                );
            case 'plus':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="16"></line>
                        <line x1="8" y1="12" x2="16" y2="12"></line>
                    </svg>
                );
            default:
                return null;
        }
    };

    return (
        <div className="min-h-screen text-white relative">
            {/* Animation Constellation en arrière-plan */}
            <ConstellationBackground />

            {/* Hero Section */}
            <section className="relative z-10 py-20 text-center">
                <div className="container mx-auto px-6">
                    <h1 className="text-5xl md:text-6xl font-bold mb-6">
                        Flexo<span className="bg-gradient-to-r from-brand-blue to-brand-purple bg-clip-text text-transparent">Div</span> Apps
                    </h1>
                    <p className="text-xl md:text-2xl text-brand-muted max-w-4xl mx-auto leading-relaxed">
                        Collection d'applications web innovantes développées avec l'IA. 
                        Découvrez nos outils créés pour simplifier votre quotidien professionnel.
                    </p>
                </div>
            </section>

            {/* Applications Grid */}
            <section className="relative z-10 py-16">
                <div className="container mx-auto px-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {appsData.map((app, index) => (
                            <div
                                key={index}
                                className="neon-card"
                            >
                                {/* App Icon */}
                                <div className={`relative z-10 ${
                                    app.status === 'coming-soon' ? 'opacity-60' : ''
                                }`}>
                                    <div className="flex items-center mb-4">
                                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center mr-4 ${
                                            app.status === 'active' 
                                                ? 'bg-gradient-to-r from-brand-blue to-brand-purple' 
                                                : 'bg-gradient-to-r from-gray-500 to-gray-600'
                                        }`}>
                                            <div className="text-white">
                                                {getIcon(app.icon)}
                                            </div>
                                        </div>
                                        <h3 className={`text-xl font-bold ${
                                            app.status === 'active' ? 'text-white' : 'text-gray-400'
                                        }`}>
                                            {app.title}
                                        </h3>
                                    </div>
                                    
                                    <p className={`mb-6 leading-relaxed ${
                                        app.status === 'active' ? 'text-brand-muted' : 'text-gray-500'
                                    }`}>
                                        {app.description}
                                    </p>
                                    
                                    {/* Tags */}
                                    <div className="flex flex-wrap gap-2 mb-6">
                                        {app.tags.map((tag, tagIndex) => (
                                            <span 
                                                key={tagIndex} 
                                                className={`px-3 py-1 rounded-full text-sm ${
                                                    app.status === 'active'
                                                        ? tagIndex === 0 
                                                            ? 'bg-blue-500/20 text-blue-300'
                                                            : tagIndex === 1
                                                            ? 'bg-purple-500/20 text-purple-300'
                                                            : 'bg-green-500/20 text-green-300'
                                                        : 'bg-gray-500/20 text-gray-400'
                                                }`}
                                            >
                                                {tag}
                                            </span>
                                        ))}
                                    </div>
                                    
                                    {/* Action Button */}
                                    {app.status === 'active' ? (
                                        app.link.startsWith('/') ? (
                                            <Link
                                                to={app.link}
                                                className="inline-flex items-center gap-2 bg-gradient-to-r from-brand-blue to-brand-purple text-white font-semibold py-3 px-6 rounded-lg hover:shadow-lg hover:shadow-brand-blue/25 transition-all duration-300 hover:transform hover:translateY(-1px)"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                                    <polyline points="15,3 21,3 21,9"></polyline>
                                                    <line x1="10" y1="14" x2="21" y2="3"></line>
                                                </svg>
                                                Lancer l'app
                                            </Link>
                                        ) : (
                                            <a
                                                href={app.link}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="inline-flex items-center gap-2 bg-gradient-to-r from-brand-blue to-brand-purple text-white font-semibold py-3 px-6 rounded-lg hover:shadow-lg hover:shadow-brand-blue/25 transition-all duration-300 hover:transform hover:translateY(-1px)"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                                    <polyline points="15,3 21,3 21,9"></polyline>
                                                    <line x1="10" y1="14" x2="21" y2="3"></line>
                                                </svg>
                                                Lancer l'app
                                            </a>
                                        )
                                    ) : (
                                        <button 
                                            disabled 
                                            className="inline-flex items-center gap-2 bg-gray-600 text-gray-300 font-semibold py-3 px-6 rounded-lg cursor-not-allowed opacity-50"
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <polyline points="10,15 15,10 21,16"></polyline>
                                                <path d="m9 11 3 3L22 4"></path>
                                            </svg>
                                            En développement
                                        </button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Call to Action */}
            <section className="relative z-10 py-20 text-center">
                <div className="container mx-auto px-6">
                    <h2 className="text-3xl md:text-4xl font-bold mb-6">
                        Une idée d'application ?
                    </h2>
                    <p className="text-xl text-brand-muted mb-8 max-w-3xl mx-auto">
                        Vous avez une idée d'application web ? Discutons ensemble de votre projet et créons quelque chose d'innovant avec l'IA.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <a 
                            href="/contact" 
                            className="bg-gradient-to-r from-brand-blue to-brand-purple text-white font-bold py-3 px-8 rounded-lg hover:scale-105 transition-transform"
                        >
                            Discuter de votre projet
                        </a>
                        <a 
                            href="/portfolio" 
                            className="bg-brand-surface text-white font-bold py-3 px-8 rounded-lg border border-brand-surface-light hover:border-brand-purple transition-all"
                        >
                            Voir mes réalisations
                        </a>
                    </div>
                </div>
            </section>
        </div>
    );
};

export default Apps;
