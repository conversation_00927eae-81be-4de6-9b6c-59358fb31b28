# **06_GESTION-ERREURS.MD - GESTION D'ERREURS ROBUSTE**

## **OBJECTIF DE LA GESTION D'ERREURS**

Ce document définit la stratégie complète de gestion d'erreurs pour garantir une expérience utilisateur fluide et un debugging efficace. Toute application doit anticiper et gérer gracieusement tous les cas d'échec possibles.

---

## **1. STRATÉGIE GLOBALE DE GESTION D'ERREURS**

### **1.1 Principes Fondamentaux**

**Règles obligatoires :**
- **Anticipation** : Prévoir tous les cas d'échec possibles
- **Graceful degradation** : L'application continue de fonctionner même en cas d'erreur
- **Feedback utilisateur** : Messages clairs et actionnables
- **Logging technique** : Traçabilité complète pour le debugging
- **Recovery** : Possibilité de récupération automatique ou manuelle

### **1.2 Types d'Erreurs à Gérer**

**Classification obligatoire :**
1. **Erreurs réseau** : Perte de connexion, timeouts, erreurs serveur
2. **Erreurs de validation** : Données invalides, contraintes non respectées
3. **Erreurs d'authentification** : Token expiré, permissions insuffisantes
4. **Erreurs de logique métier** : Règles business non respectées
5. **Erreurs techniques** : Bugs, exceptions non prévues
6. **Erreurs de ressources** : Fichiers manquants, quotas dépassés

---

## **2. GESTION D'ERREURS CÔTÉ CLIENT**

### **2.1 Error Boundaries React**

**Implémentation obligatoire :**
```typescript
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  children: React.ReactNode
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ errorInfo })
    
    // Logging de l'erreur
    console.error('Error Boundary caught an error:', error, errorInfo)
    
    // Callback personnalisé
    this.props.onError?.(error, errorInfo)
    
    // Envoi à un service de monitoring (optionnel)
    // sendErrorToMonitoring(error, errorInfo)
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback
      return <FallbackComponent error={this.state.error!} retry={this.retry} />
    }

    return this.props.children
  }
}

// Composant de fallback par défaut
const DefaultErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({ 
  error, 
  retry 
}) => (
  <div className="error-boundary-fallback">
    <h2>Une erreur inattendue s'est produite</h2>
    <p>Nous nous excusons pour ce désagrément.</p>
    <details className="error-details">
      <summary>Détails techniques</summary>
      <pre>{error.message}</pre>
    </details>
    <div className="error-actions">
      <Button onClick={retry}>Réessayer</Button>
      <Button variant="outline" onClick={() => window.location.reload()}>
        Recharger la page
      </Button>
    </div>
  </div>
)
```

### **2.2 Hook de Gestion d'Erreurs**

**Hook personnalisé pour la gestion d'état :**
```typescript
interface UseErrorHandlerReturn {
  error: string | null
  setError: (error: string | null) => void
  clearError: () => void
  handleError: (error: unknown) => void
}

export const useErrorHandler = (): UseErrorHandlerReturn => {
  const [error, setError] = useState<string | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const handleError = useCallback((error: unknown) => {
    let errorMessage = 'Une erreur inattendue s\'est produite'

    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    } else if (error && typeof error === 'object' && 'message' in error) {
      errorMessage = String(error.message)
    }

    setError(errorMessage)
    console.error('Error handled:', error)
  }, [])

  return {
    error,
    setError,
    clearError,
    handleError
  }
}
```

### **2.3 Gestion d'Erreurs Async/Await**

**Pattern obligatoire pour les opérations asynchrones :**
```typescript
const useAsyncOperation = () => {
  const [loading, setLoading] = useState(false)
  const { error, handleError, clearError } = useErrorHandler()

  const executeAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: unknown) => void
  ) => {
    try {
      setLoading(true)
      clearError()
      
      const result = await operation()
      onSuccess?.(result)
      return result
    } catch (err) {
      handleError(err)
      onError?.(err)
      throw err // Re-throw pour permettre la gestion locale si nécessaire
    } finally {
      setLoading(false)
    }
  }, [handleError, clearError])

  return {
    loading,
    error,
    executeAsync,
    clearError
  }
}

// Utilisation dans un composant
const DataComponent: React.FC = () => {
  const [data, setData] = useState(null)
  const { loading, error, executeAsync } = useAsyncOperation()

  const fetchData = useCallback(async () => {
    await executeAsync(
      () => apiService.getData(),
      (result) => setData(result),
      (error) => console.error('Failed to fetch data:', error)
    )
  }, [executeAsync])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  if (loading) return <LoadingSpinner />
  if (error) return <ErrorMessage message={error} onRetry={fetchData} />
  if (!data) return <EmptyState />

  return <DataDisplay data={data} />
}
```

---

## **3. GESTION D'ERREURS RÉSEAU ET API**

### **3.1 Intercepteur d'Erreurs HTTP**

**Configuration pour les appels API :**
```typescript
// services/apiClient.ts
interface ApiError {
  message: string
  status: number
  code?: string
  details?: unknown
}

class ApiClient {
  private baseURL: string

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      
      const apiError: ApiError = {
        message: errorData.message || this.getDefaultErrorMessage(response.status),
        status: response.status,
        code: errorData.code,
        details: errorData
      }

      throw apiError
    }

    return response.json()
  }

  private getDefaultErrorMessage(status: number): string {
    switch (status) {
      case 400: return 'Requête invalide'
      case 401: return 'Authentification requise'
      case 403: return 'Accès non autorisé'
      case 404: return 'Ressource non trouvée'
      case 429: return 'Trop de requêtes, veuillez patienter'
      case 500: return 'Erreur serveur interne'
      case 502: return 'Service temporairement indisponible'
      case 503: return 'Service en maintenance'
      default: return 'Erreur de connexion'
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`)
      return this.handleResponse<T>(response)
    } catch (error) {
      if (error instanceof TypeError) {
        throw new ApiError({
          message: 'Erreur de connexion réseau',
          status: 0,
          code: 'NETWORK_ERROR'
        })
      }
      throw error
    }
  }

  // Méthodes POST, PUT, DELETE similaires...
}
```

### **3.2 Retry et Circuit Breaker**

**Mécanisme de retry automatique :**
```typescript
interface RetryOptions {
  maxAttempts: number
  delay: number
  backoff: 'linear' | 'exponential'
  retryCondition?: (error: unknown) => boolean
}

export const withRetry = async <T>(
  operation: () => Promise<T>,
  options: RetryOptions
): Promise<T> => {
  const { maxAttempts, delay, backoff, retryCondition } = options
  let lastError: unknown

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error

      // Vérifier si on doit retry
      if (retryCondition && !retryCondition(error)) {
        throw error
      }

      // Dernier essai, on throw l'erreur
      if (attempt === maxAttempts) {
        throw error
      }

      // Calculer le délai
      const waitTime = backoff === 'exponential' 
        ? delay * Math.pow(2, attempt - 1)
        : delay * attempt

      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
  }

  throw lastError
}

// Utilisation
const fetchWithRetry = () => withRetry(
  () => apiClient.get('/data'),
  {
    maxAttempts: 3,
    delay: 1000,
    backoff: 'exponential',
    retryCondition: (error) => {
      // Retry seulement pour les erreurs réseau et 5xx
      return error instanceof ApiError && 
             (error.status >= 500 || error.status === 0)
    }
  }
)
```

---

## **4. VALIDATION ET GESTION D'ERREURS FORMULAIRES**

### **4.1 Validation avec Zod et React Hook Form**

**Schema de validation robuste :**
```typescript
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'

// Schema de validation
const userSchema = z.object({
  email: z
    .string()
    .min(1, 'L\'email est requis')
    .email('Format d\'email invalide'),
  password: z
    .string()
    .min(8, 'Le mot de passe doit contenir au moins 8 caractères')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
           'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword']
})

type UserFormData = z.infer<typeof userSchema>

// Composant formulaire avec gestion d'erreurs
const UserForm: React.FC = () => {
  const { error, handleError, clearError } = useErrorHandler()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema)
  })

  const onSubmit = async (data: UserFormData) => {
    try {
      setIsSubmitting(true)
      clearError()

      await apiClient.post('/users', data)
      
      // Succès
      toast.success('Utilisateur créé avec succès')
    } catch (err) {
      if (err instanceof ApiError && err.status === 409) {
        setError('email', { 
          type: 'manual', 
          message: 'Cet email est déjà utilisé' 
        })
      } else {
        handleError(err)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {error && <ErrorAlert message={error} onClose={clearError} />}
      
      <div>
        <Input
          {...register('email')}
          type="email"
          placeholder="Email"
          error={errors.email?.message}
        />
      </div>
      
      <div>
        <Input
          {...register('password')}
          type="password"
          placeholder="Mot de passe"
          error={errors.password?.message}
        />
      </div>
      
      <div>
        <Input
          {...register('confirmPassword')}
          type="password"
          placeholder="Confirmer le mot de passe"
          error={errors.confirmPassword?.message}
        />
      </div>
      
      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Création...' : 'Créer l\'utilisateur'}
      </Button>
    </form>
  )
}
```

---

## **5. COMPOSANTS D'AFFICHAGE D'ERREURS**

### **5.1 Composants d'Erreur Réutilisables**

**Composants standardisés :**
```typescript
// Composant d'alerte d'erreur
interface ErrorAlertProps {
  message: string
  onClose?: () => void
  onRetry?: () => void
  variant?: 'error' | 'warning'
}

const ErrorAlert: React.FC<ErrorAlertProps> = ({
  message,
  onClose,
  onRetry,
  variant = 'error'
}) => (
  <Alert variant={variant} className="error-alert">
    <AlertCircle className="h-4 w-4" />
    <AlertTitle>
      {variant === 'error' ? 'Erreur' : 'Attention'}
    </AlertTitle>
    <AlertDescription>{message}</AlertDescription>
    <div className="error-alert-actions">
      {onRetry && (
        <Button variant="outline" size="sm" onClick={onRetry}>
          Réessayer
        </Button>
      )}
      {onClose && (
        <Button variant="ghost" size="sm" onClick={onClose}>
          Fermer
        </Button>
      )}
    </div>
  </Alert>
)

// Composant d'état d'erreur pour pages entières
interface ErrorStateProps {
  title?: string
  message: string
  onRetry?: () => void
  showReload?: boolean
}

const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Une erreur s\'est produite',
  message,
  onRetry,
  showReload = true
}) => (
  <div className="error-state">
    <div className="error-state-icon">
      <AlertTriangle size={48} />
    </div>
    <h2 className="error-state-title">{title}</h2>
    <p className="error-state-message">{message}</p>
    <div className="error-state-actions">
      {onRetry && (
        <Button onClick={onRetry}>
          Réessayer
        </Button>
      )}
      {showReload && (
        <Button variant="outline" onClick={() => window.location.reload()}>
          Recharger la page
        </Button>
      )}
    </div>
  </div>
)
```

---

## **6. LOGGING ET MONITORING**

### **6.1 Service de Logging**

**Centralisation des logs d'erreurs :**
```typescript
enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

interface LogEntry {
  level: LogLevel
  message: string
  timestamp: Date
  context?: Record<string, unknown>
  error?: Error
  userId?: string
  sessionId?: string
}

class Logger {
  private logs: LogEntry[] = []
  private maxLogs = 1000

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error
  ): LogEntry {
    return {
      level,
      message,
      timestamp: new Date(),
      context,
      error,
      userId: getCurrentUserId(),
      sessionId: getSessionId()
    }
  }

  debug(message: string, context?: Record<string, unknown>) {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context)
    this.addLog(entry)
    console.debug(message, context)
  }

  info(message: string, context?: Record<string, unknown>) {
    const entry = this.createLogEntry(LogLevel.INFO, message, context)
    this.addLog(entry)
    console.info(message, context)
  }

  warn(message: string, context?: Record<string, unknown>) {
    const entry = this.createLogEntry(LogLevel.WARN, message, context)
    this.addLog(entry)
    console.warn(message, context)
  }

  error(message: string, error?: Error, context?: Record<string, unknown>) {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error)
    this.addLog(entry)
    console.error(message, error, context)
    
    // Envoi vers service de monitoring en production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoring(entry)
    }
  }

  private addLog(entry: LogEntry) {
    this.logs.push(entry)
    if (this.logs.length > this.maxLogs) {
      this.logs.shift()
    }
  }

  private async sendToMonitoring(entry: LogEntry) {
    // Implémentation de l'envoi vers un service de monitoring
    // (Sentry, LogRocket, etc.)
  }

  getLogs(level?: LogLevel): LogEntry[] {
    return level 
      ? this.logs.filter(log => log.level === level)
      : this.logs
  }
}

export const logger = new Logger()
```

---

## **7. TESTS DE GESTION D'ERREURS**

### **7.1 Tests des Error Boundaries**

**Tests unitaires obligatoires :**
```typescript
import { render, screen } from '@testing-library/react'
import { ErrorBoundary } from './ErrorBoundary'

const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

describe('ErrorBoundary', () => {
  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('No error')).toBeInTheDocument()
  })

  it('renders error fallback when there is an error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText(/Une erreur inattendue s'est produite/)).toBeInTheDocument()
  })

  it('calls onError callback when error occurs', () => {
    const onError = vi.fn()
    
    render(
      <ErrorBoundary onError={onError}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.any(Object)
    )
  })
})
```

---

**⚠️ IMPORTANT :** La gestion d'erreurs est critique pour l'expérience utilisateur et la maintenabilité de l'application. Chaque composant et service doit implémenter une gestion d'erreurs appropriée selon les patterns définis dans ce document.
