
import React from 'react';
import AIChat from '../components/AIChat';
import '../css/glowing-effect.css';

const Contact: React.FC = () => {
    return (
        <div className="container mx-auto px-6 py-20">
            <section className="text-center max-w-4xl mx-auto mb-16">
                <h1 className="text-5xl font-bold mb-4">Discutons d'une collaboration</h1>
                <p className="text-xl text-brand-muted">
                    Vous cherchez un développeur pour renforcer votre équipe ? Contactez-moi pour discuter d'une mission intérim ou posez vos questions à mon assistant IA.
                </p>
            </section>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
                <div className="neon-card">
                    <h3 className="text-2xl font-bold mb-6 text-white">Envoyez-moi un message</h3>
                    <form className="space-y-6">
                        <div>
                            <label htmlFor="name" className="block text-sm font-medium text-brand-muted mb-1">Nom complet</label>
                            <input type="text" id="name" className="w-full bg-brand-surface rounded-lg p-3 text-brand-light placeholder-brand-muted focus:outline-none focus:ring-2 focus:ring-brand-purple" placeholder="Votre nom" />
                        </div>
                        <div>
                            <label htmlFor="email" className="block text-sm font-medium text-brand-muted mb-1">Email</label>
                            <input type="email" id="email" className="w-full bg-brand-surface rounded-lg p-3 text-brand-light placeholder-brand-muted focus:outline-none focus:ring-2 focus:ring-brand-purple" placeholder="<EMAIL>" />
                        </div>
                        <div>
                            <label htmlFor="company" className="block text-sm font-medium text-brand-muted mb-1">Entreprise (optionnel)</label>
                            <input type="text" id="company" className="w-full bg-brand-surface rounded-lg p-3 text-brand-light placeholder-brand-muted focus:outline-none focus:ring-2 focus:ring-brand-purple" placeholder="Nom de votre entreprise" />
                        </div>
                        <div>
                            <label htmlFor="message" className="block text-sm font-medium text-brand-muted mb-1">Décrivez vos besoins</label>
                            <textarea id="message" rows={4} className="w-full bg-brand-surface rounded-lg p-3 text-brand-light placeholder-brand-muted focus:outline-none focus:ring-2 focus:ring-brand-purple" placeholder="Décrivez le contexte de la mission, les technologies utilisées et vos attentes..."></textarea>
                        </div>
                        <div>
                            <button type="submit" className="w-full bg-gradient-to-r from-brand-blue to-brand-purple text-white font-bold py-3 px-8 rounded-lg hover:scale-105 transition-transform">
                                Envoyer le message
                            </button>
                        </div>
                    </form>
                </div>

                <div className="h-fit">
                    <AIChat />
                </div>
            </div>
        </div>
    );
};

export default Contact;